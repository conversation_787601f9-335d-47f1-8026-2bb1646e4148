#!/usr/bin/env python3
"""
Debug timing issue with API calls
"""

import asyncio
import httpx
import json
import time
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def debug_timing_issue():
    """Debug timing issue with API calls"""
    print("🔍 Debugging Timing Issue with API Calls")
    print("=" * 60)
    
    try:
        # Get current rooms first
        client = create_client()
        rooms = await client.get_rooms()
        
        print("1️⃣ Current rooms:")
        for room in rooms:
            print(f"   - '{room.get('localityTitle')}' (ID: {room.get('localityID')})")
        
        if not rooms:
            print("❌ No rooms found")
            return
        
        # Find a room to test with
        test_room = None
        for room in rooms:
            if room.get('localityTitle') in ['Room1', 'MyNewRoom', 'Room 2']:
                test_room = room.get('localityTitle')
                break
        
        if not test_room:
            test_room = rooms[0].get('localityTitle')
        
        print(f"\n2️⃣ Testing update with room: '{test_room}'")
        
        # Test direct API call with detailed timing
        print(f"\n3️⃣ Direct API call test:")
        
        update_data = {
            "localityTitle": f"Updated{int(time.time())}",
            "isActive": True,
            "localityCode": f"UPD{int(time.time()) % 10000}"
        }
        
        start_time = time.time()
        
        async with httpx.AsyncClient(timeout=30.0) as http_client:
            try:
                print(f"   Making API call at {time.strftime('%H:%M:%S')}")
                
                response = await http_client.put(
                    f"http://localhost:5077/api/Configuration/rooms/update-by-name/{test_room}",
                    json=update_data
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                print(f"   Response received at {time.strftime('%H:%M:%S')} (took {duration:.2f}s)")
                print(f"   Status: {response.status_code}")
                print(f"   Response: {response.text}")
                
                # Check if the update actually worked by getting rooms again
                print(f"\n   Checking if update worked...")
                rooms_after = await client.get_rooms()
                
                updated_room = None
                for room in rooms_after:
                    if room.get('localityTitle') == update_data['localityTitle']:
                        updated_room = room
                        break
                
                if updated_room:
                    print(f"   ✅ Update successful! Found room: '{updated_room.get('localityTitle')}'")
                else:
                    print(f"   ❌ Update failed - room not found with new name")
                    print(f"   Current rooms:")
                    for room in rooms_after:
                        print(f"     - '{room.get('localityTitle')}'")
                
            except asyncio.TimeoutError:
                print(f"   ⏱️ API call timed out after 30 seconds")
            except Exception as e:
                print(f"   ❌ API call error: {e}")
        
        # Test chatbot functionality with timing
        print(f"\n4️⃣ Chatbot test:")
        
        # Get current rooms again
        current_rooms = await client.get_rooms()
        if current_rooms:
            test_room_chatbot = current_rooms[0].get('localityTitle')
            new_name = f"ChatbotTest{int(time.time())}"
            
            print(f"   Testing chatbot update: '{test_room_chatbot}' to '{new_name}'")
            
            chatbot = create_llm_chatbot('mock', client)
            
            start_time = time.time()
            
            try:
                result = await asyncio.wait_for(
                    chatbot.process_message('test-session', f'update room name {test_room_chatbot} to {new_name}'),
                    timeout=30.0
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                print(f"   Chatbot response received in {duration:.2f}s")
                print(f"   Result type: {result.get('type')}")
                print(f"   Content preview: {result.get('content', '')[:200]}...")
                
                # Check if the update actually worked
                print(f"\n   Checking if chatbot update worked...")
                rooms_after_chatbot = await client.get_rooms()
                
                chatbot_updated_room = None
                for room in rooms_after_chatbot:
                    if room.get('localityTitle') == new_name:
                        chatbot_updated_room = room
                        break
                
                if chatbot_updated_room:
                    print(f"   ✅ Chatbot update successful! Found room: '{chatbot_updated_room.get('localityTitle')}'")
                    
                    # Check if the chatbot reported success or error
                    if result.get('type') == 'success':
                        print(f"   ✅ Chatbot correctly reported success")
                    else:
                        print(f"   ❌ Chatbot incorrectly reported error, but update worked!")
                        print(f"   This is the bug - update works but chatbot reports failure")
                else:
                    print(f"   ❌ Chatbot update failed - room not found with new name")
                
            except asyncio.TimeoutError:
                print(f"   ⏱️ Chatbot call timed out after 30 seconds")
            except Exception as e:
                print(f"   ❌ Chatbot error: {e}")
        
        print(f"\n✅ Timing debug completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(debug_timing_issue())
