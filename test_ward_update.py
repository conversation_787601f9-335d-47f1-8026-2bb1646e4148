#!/usr/bin/env python3
"""
Test ward update functionality specifically
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_ward_update():
    """Test ward update functionality"""
    print("🧪 Testing Ward Update Functionality")
    print("=" * 50)
    
    try:
        # Create fresh instances
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Get current wards
        print("1️⃣ Getting current wards:")
        wards = await client.get_wards()
        
        print(f"Found {len(wards)} wards:")
        for ward in wards:
            print(f"   - '{ward.get('localityTitle')}' (Code: {ward.get('localityCode')}, ID: {ward.get('localityID')})")
        
        if not wards:
            print("❌ No wards found to test with")
            return
        
        # Test ward update
        test_ward = wards[0]['localityTitle']
        new_ward_name = f"UpdatedWard{int(asyncio.get_event_loop().time())}"
        
        print(f"\n2️⃣ Testing ward update:")
        print(f"   Updating '{test_ward}' to '{new_ward_name}'")
        
        # Test the message parsing
        test_message = f"update ward name {test_ward} to {new_ward_name}"
        print(f"   Message: '{test_message}'")
        
        try:
            result = await asyncio.wait_for(
                chatbot.process_message('test-session', test_message),
                timeout=20.0
            )
            
            print(f"   Result type: {result.get('type')}")
            if result.get('type') == 'success':
                print(f"   ✅ Ward update SUCCESS!")
                print(f"   Response: {result.get('content', '')[:300]}...")
            else:
                print(f"   ❌ Ward update FAILED:")
                print(f"   Response: {result.get('content', '')[:400]}...")
                
        except asyncio.TimeoutError:
            print(f"   ⏱️ Ward update timeout")
        except Exception as e:
            print(f"   ❌ Ward update error: {e}")
        
        # Verify the update
        print(f"\n3️⃣ Verifying the update:")
        try:
            updated_wards = await client.get_wards()
            
            # Look for the updated ward
            found_updated = False
            for ward in updated_wards:
                if ward.get('localityTitle') == new_ward_name:
                    print(f"   ✅ Found updated ward '{new_ward_name}':")
                    print(f"      ID: {ward.get('localityID')}")
                    print(f"      Code: {ward.get('localityCode')}")
                    print(f"      Building: {ward.get('parentLocalityTitle')}")
                    found_updated = True
                    break
            
            if not found_updated:
                print(f"   ❌ Updated ward '{new_ward_name}' not found")
                print("   Current wards:")
                for ward in updated_wards:
                    print(f"      - {ward.get('localityTitle')}")
                    
        except Exception as e:
            print(f"   ❌ Error verifying update: {e}")
        
        print(f"\n✅ Ward update test completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_ward_update())
