#!/usr/bin/env python3
"""
Test the improved response format
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_improved_response():
    """Test the improved response format"""
    print("🧪 Testing Improved Response Format")
    print("=" * 60)
    
    try:
        # Get current rooms
        client = create_client()
        rooms = await client.get_rooms()
        
        print("1️⃣ Current rooms:")
        for room in rooms:
            print(f"   - '{room.get('localityTitle')}'")
        
        if not rooms:
            print("❌ No rooms found")
            return
        
        # Find a room to test with
        test_room = rooms[0].get('localityTitle')
        new_name = f"ImprovedTest{int(asyncio.get_event_loop().time())}"
        
        print(f"\n2️⃣ Testing improved response format:")
        print(f"   Updating '{test_room}' to '{new_name}'")
        
        # Test the chatbot with the new response format
        chatbot = create_llm_chatbot('mock', client)
        
        try:
            result = await asyncio.wait_for(
                chatbot.process_message('test-session', f'update room name {test_room} to {new_name}'),
                timeout=30.0
            )
            
            print(f"   Result type: {result.get('type')}")
            print(f"\n   Full Response:")
            print("   " + "="*50)
            print(result.get('content', ''))
            print("   " + "="*50)
            
            # Check if response contains table and no IDs
            content = result.get('content', '')
            
            if 'Updated Room List:' in content:
                print(f"\n   ✅ Response includes updated room list table!")
            else:
                print(f"\n   ❌ Response missing updated room list table")
            
            if 'Room ID:' not in content and 'localityID' not in content:
                print(f"   ✅ No IDs shown in response!")
            else:
                print(f"   ❌ IDs still visible in response")
            
            if result.get('type') == 'success':
                print(f"   ✅ Update reported as successful!")
            else:
                print(f"   ❌ Update not reported as successful")
                
        except asyncio.TimeoutError:
            print(f"   ⏱️ Timeout")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print(f"\n✅ Improved response test completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_improved_response())
