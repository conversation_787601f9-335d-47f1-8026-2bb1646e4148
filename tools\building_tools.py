"""
Building management tools for MCP
"""

from typing import Any, Dict, List
from mcp.types import Tool, TextContent
from .base_tool import BaseTool


class BuildingTools(BaseTool):
    """Tools for building management"""
    
    def get_tool_definition(self) -> Tool:
        """Get buildings tool definition"""
        return Tool(
            name="get_buildings",
            description="Get all hospital buildings in the IPU system",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute get buildings"""
        buildings = await self.client.get_buildings()

        if not buildings:
            return [TextContent(type="text", text="🏢 No buildings found in the system.")]

        # Format buildings using actual API field names
        text = "🏢 **Hospital Buildings**\n\n"
        for building in buildings:
            text += f"**{building.get('localityTitle', 'Unknown')}** (ID: {building.get('localityID')})\n"
            text += f"• Code: {building.get('localityCode', 'N/A')}\n"
            if building.get('localityDescription'):
                text += f"• Description: {building.get('localityDescription')}\n"
            text += f"• Wards: {building.get('childCount', 0)}\n"
            text += f"• Status: {'Active' if building.get('isActive', False) else 'Inactive'}\n"
            if building.get('facilities'):
                text += f"• Facilities: {building.get('facilities')}\n"
            text += "\n"

        return [TextContent(type="text", text=text)]


class BuildingDetailsTools(BaseTool):
    """Tools for getting building details"""
    
    def get_tool_definition(self) -> Tool:
        """Get building details tool definition"""
        return Tool(
            name="get_building_details",
            description="Get detailed information about a specific building by ID",
            inputSchema={
                "type": "object",
                "properties": {
                    "building_id": {
                        "type": "integer",
                        "description": "The ID of the building to retrieve"
                    }
                },
                "required": ["building_id"]
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute get building details"""
        # Validate arguments
        error = self.validate_required_args(arguments, ["building_id"])
        if error:
            return self.create_error_response(error)
        
        error = self.validate_integer_arg(arguments, "building_id")
        if error:
            return self.create_error_response(error)
        
        building_id = int(arguments["building_id"])
        building = await self.client.get_building_by_id(building_id)
        
        if not building:
            return [TextContent(type="text", text=f"🏢 Building with ID {building_id} not found.")]
        
        # Format building details using actual API field names
        details = f"""🏢 **Building Details**

**Name:** {building.get('localityTitle', 'N/A')}
**Code:** {building.get('localityCode', 'N/A')}
**Description:** {building.get('localityDescription', 'No description available')}
**Status:** {'Active' if building.get('isActive', False) else 'Inactive'}
**Wards:** {building.get('childCount', 0)}
**Type:** {building.get('localityTypeName', 'Building')}
**Facilities:** {building.get('facilities', 'None specified')}
**Multiple Rooms:** {'Yes' if building.get('isMultipleRoom', False) else 'No'}
**Home Leave Allowed:** {'Yes' if building.get('isHomeLeave', False) else 'No'}
**Created:** {building.get('insertedAt', 'N/A')}
**Last Updated:** {building.get('updatedAt', 'Never')}
"""
        
        return [TextContent(type="text", text=details)]


class UpdateBuildingTools(BaseTool):
    """Tools for updating building information"""

    def get_tool_definition(self) -> Tool:
        """Get update building tool definition"""
        return Tool(
            name="update_building",
            description="Update building information by ID",
            inputSchema={
                "type": "object",
                "properties": {
                    "building_id": {
                        "type": "integer",
                        "description": "The ID of the building to update"
                    },
                    "name": {
                        "type": "string",
                        "description": "New building name (localityTitle)"
                    },
                    "code": {
                        "type": "string",
                        "description": "New building code (localityCode)"
                    },
                    "description": {
                        "type": "string",
                        "description": "New building description (localityDescription)"
                    },
                    "facilities": {
                        "type": "string",
                        "description": "New facilities information"
                    }
                },
                "required": ["building_id"]
            }
        )

    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute update building"""
        building_id = arguments.get("building_id")

        if not building_id:
            return [TextContent(type="text", text="❌ Building ID is required")]

        # Prepare update data
        update_data = {}
        if arguments.get("name"):
            update_data["localityTitle"] = arguments["name"]
        if arguments.get("code"):
            update_data["localityCode"] = arguments["code"]
        if arguments.get("description"):
            update_data["localityDescription"] = arguments["description"]
        if arguments.get("facilities"):
            update_data["facilities"] = arguments["facilities"]

        if not update_data:
            return [TextContent(type="text", text="❌ At least one field must be provided for update")]

        try:
            # Get current building info first
            current_building = await self.client.get_building_by_id(building_id)
            if not current_building:
                return [TextContent(type="text", text=f"❌ Building with ID {building_id} not found")]

            # Update the building
            updated_building = await self.client.update_building(building_id, update_data)

            # Format success response
            text = f"✅ **Building Updated Successfully**\n\n"
            text += f"**Building ID:** {building_id}\n"
            text += f"**Name:** {updated_building.get('localityTitle', 'N/A')}\n"
            text += f"**Code:** {updated_building.get('localityCode', 'N/A')}\n"
            text += f"**Description:** {updated_building.get('localityDescription', 'N/A')}\n"
            if updated_building.get('facilities'):
                text += f"**Facilities:** {updated_building.get('facilities')}\n"
            text += f"**Last Updated:** {updated_building.get('updatedAt', 'Now')}\n"

            return [TextContent(type="text", text=text)]

        except Exception as e:
            return [TextContent(type="text", text=f"❌ Error updating building: {str(e)}")]


class CreateBuildingTools(BaseTool):
    """Tools for creating new buildings"""

    def get_tool_definition(self) -> Tool:
        """Get create building tool definition"""
        return Tool(
            name="create_building",
            description="Create a new building",
            inputSchema={
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string",
                        "description": "Building name (localityTitle)"
                    },
                    "code": {
                        "type": "string",
                        "description": "Building code (localityCode)"
                    },
                    "description": {
                        "type": "string",
                        "description": "Building description (localityDescription)"
                    },
                    "facilities": {
                        "type": "string",
                        "description": "Facilities information"
                    }
                },
                "required": ["name", "code"]
            }
        )

    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute create building"""
        name = arguments.get("name")
        code = arguments.get("code")

        if not name or not code:
            return [TextContent(type="text", text="❌ Building name and code are required")]

        # Prepare building data
        building_data = {
            "localityTitle": name,
            "localityCode": code,
            "localityTypeID": 1,  # Building type
            "isActive": True
        }

        if arguments.get("description"):
            building_data["localityDescription"] = arguments["description"]
        if arguments.get("facilities"):
            building_data["facilities"] = arguments["facilities"]

        try:
            # Create the building
            new_building = await self.client.create_building(building_data)

            # Format success response
            text = f"✅ **Building Created Successfully**\n\n"
            text += f"**Building ID:** {new_building.get('localityID', 'N/A')}\n"
            text += f"**Name:** {new_building.get('localityTitle', 'N/A')}\n"
            text += f"**Code:** {new_building.get('localityCode', 'N/A')}\n"
            text += f"**Description:** {new_building.get('localityDescription', 'N/A')}\n"
            if new_building.get('facilities'):
                text += f"**Facilities:** {new_building.get('facilities')}\n"
            text += f"**Created:** {new_building.get('insertedAt', 'Now')}\n"

            return [TextContent(type="text", text=text)]

        except Exception as e:
            return [TextContent(type="text", text=f"❌ Error creating building: {str(e)}")]


class UpdateBuildingByNameTools(BaseTool):
    """Tools for updating building by name (using the new API endpoint)"""

    def get_tool_definition(self) -> Tool:
        """Get update building by name tool definition"""
        return Tool(
            name="update_building_by_name",
            description="Update building information by name using the new API endpoint",
            inputSchema={
                "type": "object",
                "properties": {
                    "building_name": {
                        "type": "string",
                        "description": "The current name of the building to update"
                    },
                    "new_name": {
                        "type": "string",
                        "description": "New building name (localityTitle)"
                    },
                    "code": {
                        "type": "string",
                        "description": "New building code (localityCode)"
                    },
                    "description": {
                        "type": "string",
                        "description": "New building description (localityDescription)"
                    },
                    "facilities": {
                        "type": "string",
                        "description": "New facilities information"
                    }
                },
                "required": ["building_name"]
            }
        )

    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute update building by name"""
        building_name = arguments.get("building_name")

        if not building_name:
            return [TextContent(type="text", text="❌ Building name is required")]

        # Prepare update data using UpdateLocalityDto format
        update_data = {}
        if arguments.get("new_name"):
            update_data["localityTitle"] = arguments["new_name"]
        if arguments.get("code"):
            update_data["localityCode"] = arguments["code"]
        if arguments.get("description"):
            update_data["localityDescription"] = arguments["description"]
        if arguments.get("facilities"):
            update_data["facilities"] = arguments["facilities"]

        if not update_data:
            return [TextContent(type="text", text="❌ At least one field must be provided for update")]

        try:
            # Update the building by name using the new API endpoint
            updated_building = await self.client.update_building_by_name(building_name, update_data)

            # Format success response
            text = f"✅ **Building Updated Successfully**\n\n"
            text += f"**Previous Name:** {building_name}\n"
            text += f"**Current Name:** {updated_building.get('localityTitle', 'N/A')}\n"
            text += f"**Code:** {updated_building.get('localityCode', 'N/A')}\n"
            text += f"**Description:** {updated_building.get('localityDescription', 'N/A')}\n"
            if updated_building.get('facilities'):
                text += f"**Facilities:** {updated_building.get('facilities')}\n"
            text += f"**Building ID:** {updated_building.get('localityID', 'N/A')}\n"
            text += f"**Last Updated:** {updated_building.get('updatedAt', 'Now')}\n"

            return [TextContent(type="text", text=text)]

        except Exception as e:
            return [TextContent(type="text", text=f"❌ Error updating building '{building_name}': {str(e)}")]


class DeleteBuildingTools(BaseTool):
    """Tools for deleting buildings"""

    def get_tool_definition(self) -> Tool:
        """Get delete building tool definition"""
        return Tool(
            name="delete_building",
            description="Delete a building by ID",
            inputSchema={
                "type": "object",
                "properties": {
                    "building_id": {
                        "type": "integer",
                        "description": "The ID of the building to delete"
                    }
                },
                "required": ["building_id"]
            }
        )

    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute delete building"""
        building_id = arguments.get("building_id")

        if not building_id:
            return [TextContent(type="text", text="❌ Building ID is required")]

        try:
            # Get building info first for confirmation
            building = await self.client.get_building_by_id(building_id)
            if not building:
                return [TextContent(type="text", text=f"❌ Building with ID {building_id} not found")]

            # Delete the building
            success = await self.client.delete_building(building_id)

            if success:
                text = f"✅ **Building Deleted Successfully**\n\n"
                text += f"**Deleted Building:** {building.get('localityTitle', 'Unknown')}\n"
                text += f"**Building ID:** {building_id}\n"
                text += f"**Code:** {building.get('localityCode', 'N/A')}\n"

                return [TextContent(type="text", text=text)]
            else:
                return [TextContent(type="text", text=f"❌ Failed to delete building with ID {building_id}")]

        except Exception as e:
            return [TextContent(type="text", text=f"❌ Error deleting building: {str(e)}")]


class BuildingSearchTools(BaseTool):
    """Tools for searching buildings"""
    
    def get_tool_definition(self) -> Tool:
        """Get building search tool definition"""
        return Tool(
            name="search_buildings",
            description="Search for buildings by name, code, or description",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query to find buildings"
                    }
                },
                "required": ["query"]
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute building search"""
        # Validate arguments
        error = self.validate_required_args(arguments, ["query"])
        if error:
            return self.create_error_response(error)
        
        query = arguments["query"].strip()
        if not query:
            return self.create_error_response("Search query cannot be empty")
        
        # Use the API search endpoint
        try:
            filtered_buildings = await self.client.search_buildings(query)
        except Exception:
            # Fallback to manual filtering if search endpoint fails
            buildings = await self.client.get_buildings()
            query_lower = query.lower()

            filtered_buildings = [
                building for building in buildings
                if (query_lower in building.get('localityTitle', '').lower() or
                    query_lower in building.get('localityCode', '').lower() or
                    query_lower in building.get('localityDescription', '').lower())
            ]
        
        if not filtered_buildings:
            return [TextContent(type="text", text=f"🔍 No buildings found matching '{query}'.")]
        
        # Format results using actual field names
        text = f"🔍 **Buildings matching '{query}'**\n\n"
        for building in filtered_buildings:
            text += f"**{building.get('localityTitle', 'Unknown')}** (ID: {building.get('localityID')})\n"
            text += f"• Code: {building.get('localityCode', 'N/A')}\n"
            if building.get('localityDescription'):
                text += f"• Description: {building.get('localityDescription')}\n"
            text += f"• Wards: {building.get('childCount', 0)}\n"
            text += "\n"
        
        return [TextContent(type="text", text=text)]


class BuildingStatsTools(BaseTool):
    """Tools for building statistics"""
    
    def get_tool_definition(self) -> Tool:
        """Get building stats tool definition"""
        return Tool(
            name="get_building_stats",
            description="Get statistics about buildings in the system",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute building stats"""
        # Note: arguments is not used for this endpoint but required by interface
        _ = arguments

        buildings = await self.client.get_buildings()

        if not buildings:
            return [TextContent(type="text", text="📊 No buildings found to analyze.")]

        # Calculate stats using actual API field names
        total_buildings = len(buildings)
        active_buildings = sum(1 for b in buildings if b.get('isActive', False))
        inactive_buildings = total_buildings - active_buildings

        # Use childCount from API response instead of making additional calls
        total_wards = sum(building.get('childCount', 0) for building in buildings)

        # Format stats
        stats = f"""📊 **Building Statistics**

**Total Buildings:** {total_buildings}
**Active Buildings:** {active_buildings}
**Inactive Buildings:** {inactive_buildings}
**Total Wards:** {total_wards}

**Buildings by Ward Count:**
"""

        for building in buildings:
            ward_count = building.get('childCount', 0)
            building_name = building.get('localityTitle', 'Unknown')
            stats += f"• {building_name}: {ward_count} wards\n"

        return [TextContent(type="text", text=stats)]
