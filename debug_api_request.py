#!/usr/bin/env python3
"""
Debug the API request format
"""

import asyncio
import httpx
import json

async def debug_api_request():
    """Debug what the API expects"""
    print("🔍 Debugging API Request Format")
    print("=" * 50)
    
    base_url = "http://localhost:5077"
    
    async with httpx.AsyncClient() as client:
        # First, let's check the current room data
        print("1️⃣ Getting current room 'surg' data:")
        try:
            response = await client.get(f"{base_url}/api/Configuration/rooms")
            rooms = response.json()
            
            surg_room = None
            for room in rooms.get('data', []):
                if room.get('localityTitle') == 'surg':
                    surg_room = room
                    break
            
            if surg_room:
                print("✅ Found 'surg' room:")
                print(json.dumps(surg_room, indent=2))
            else:
                print("❌ Room 'surg' not found")
                print("Available rooms:")
                for room in rooms.get('data', [])[:5]:
                    print(f"  - {room.get('localityTitle', 'Unknown')}")
        except Exception as e:
            print(f"❌ Error getting rooms: {e}")
        
        # Test different request formats for update
        print(f"\n2️⃣ Testing different update request formats:")
        
        test_data_formats = [
            # Format 1: Simple field update
            {"localityTitle": "surgicalroom"},
            
            # Format 2: Complete locality object
            {
                "localityTitle": "surgicalroom",
                "localityCode": "SURG",
                "localityDescription": "Updated surgical room"
            },
            
            # Format 3: With type information
            {
                "localityTitle": "surgicalroom",
                "localityTypeID": 3,
                "isActive": True
            },
            
            # Format 4: Minimal required fields only
            {"localityTitle": "surgicalroom"}
        ]
        
        for i, test_data in enumerate(test_data_formats, 1):
            print(f"\n   Format {i}: {json.dumps(test_data)}")
            try:
                response = await client.put(
                    f"{base_url}/api/Configuration/rooms/update-by-name/surg",
                    json=test_data,
                    timeout=5.0
                )
                print(f"   ✅ Status: {response.status_code}")
                if response.status_code == 200:
                    result = response.json()
                    print(f"   ✅ Success: {result}")
                    break
                else:
                    print(f"   ❌ Response: {response.text[:200]}")
            except httpx.TimeoutException:
                print(f"   ⏱️ Timeout")
            except Exception as e:
                print(f"   ❌ Error: {str(e)[:100]}")
        
        # Check if the endpoint exists with different HTTP methods
        print(f"\n3️⃣ Testing HTTP methods on the endpoint:")
        methods = ['GET', 'POST', 'PUT', 'PATCH']
        
        for method in methods:
            try:
                if method == 'GET':
                    response = await client.get(f"{base_url}/api/Configuration/rooms/update-by-name/surg")
                elif method == 'POST':
                    response = await client.post(f"{base_url}/api/Configuration/rooms/update-by-name/surg", json={"localityTitle": "test"})
                elif method == 'PUT':
                    response = await client.put(f"{base_url}/api/Configuration/rooms/update-by-name/surg", json={"localityTitle": "test"})
                elif method == 'PATCH':
                    response = await client.patch(f"{base_url}/api/Configuration/rooms/update-by-name/surg", json={"localityTitle": "test"})
                
                print(f"   {method}: {response.status_code}")
                if response.status_code not in [404, 405]:
                    print(f"      Response: {response.text[:100]}")
            except Exception as e:
                print(f"   {method}: Error - {str(e)[:50]}")
        
        # Check Swagger documentation for the specific endpoint
        print(f"\n4️⃣ Checking Swagger for endpoint details:")
        try:
            swagger_response = await client.get(f"{base_url}/swagger/v1/swagger.json")
            swagger_data = swagger_response.json()
            
            # Look for the update-by-name endpoint
            paths = swagger_data.get('paths', {})
            update_endpoint = f"/api/Configuration/rooms/update-by-name/{{roomName}}"
            
            if update_endpoint in paths:
                endpoint_info = paths[update_endpoint]
                print(f"✅ Found endpoint in Swagger:")
                print(json.dumps(endpoint_info, indent=2))
            else:
                print(f"❌ Endpoint not found in Swagger")
                print("Available room endpoints:")
                for path in paths.keys():
                    if 'room' in path.lower():
                        print(f"  - {path}")
        except Exception as e:
            print(f"❌ Error getting Swagger: {e}")

if __name__ == '__main__':
    asyncio.run(debug_api_request())
