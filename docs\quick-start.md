# Quick Start Guide

## Prerequisites

1. **Python 3.8+** installed
2. **IPU Management System API** running on `http://localhost:5077`
3. **Git** (optional, for cloning)

## Installation

### 1. Clone or Download
```bash
git clone <repository-url>
cd IPU-MCP-Chatbot
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Configure (Optional)
```bash
cp .env.example .env
# Edit .env file with your settings
```

## Running the Applications

### Option 1: Interactive Chatbot (Recommended for Testing)
```bash
python chatbot.py
```

This starts an interactive command-line chatbot where you can ask questions in natural language:

```
🏥 Welcome to IPU Management Assistant!

IPU> show me all buildings
IPU> what's the occupancy rate?
IPU> find available rooms
IPU> search for cardiac rooms
```

### Option 2: MCP Server (For LLM Integration)
```bash
python mcp_server.py
```

This starts the MCP server that can be integrated with LLM clients like <PERSON>.

## Testing

### Test the System
```bash
python test_mcp.py
```

This will:
- Test API connectivity
- Validate all endpoints
- Show example MCP requests
- Display test results

## Example Commands

### Buildings
- `"show buildings"` - List all buildings
- `"building 1"` - Get details for building 1
- `"search buildings main"` - Search for buildings

### Wards
- `"show wards"` - List all wards
- `"wards in building 1"` - Get wards in building 1
- `"ward 2"` - Get details for ward 2

### Rooms
- `"show rooms"` - List all rooms
- `"available rooms"` - Show available rooms
- `"isolation rooms"` - Show isolation rooms
- `"search rooms cardiac"` - Search for rooms

### Analytics
- `"occupancy stats"` - Get occupancy statistics
- `"system stats"` - Get system overview
- `"capacity analysis"` - Analyze capacity

## Troubleshooting

### Connection Issues
1. Ensure IPU API is running: `http://localhost:5077`
2. Check firewall settings
3. Verify API endpoints are accessible

### Import Errors
```bash
pip install -r requirements.txt
```

### Configuration Issues
1. Check `.env` file settings
2. Verify API URL is correct
3. Test with `python test_mcp.py`

## Next Steps

1. **For Development**: Explore the `tools/` directory to add new functionality
2. **For Integration**: Use the MCP server with your preferred LLM client
3. **For Customization**: Modify `config.py` and `.env` settings

## Support

- Check logs for detailed error information
- Use `python test_mcp.py` to diagnose issues
- Verify IPU API is accessible and returning data
