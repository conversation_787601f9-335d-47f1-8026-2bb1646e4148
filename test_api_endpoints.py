#!/usr/bin/env python3
"""
Test API endpoints to see what's available
"""

import asyncio
import httpx

async def test_api_endpoints():
    """Test different API endpoints and ports"""
    print("🔍 Testing API Endpoints")
    print("=" * 50)
    
    # Common ports for APIs
    ports_to_test = [5077, 5000, 3000, 8080, 8000, 7000]
    
    for port in ports_to_test:
        print(f"\n🌐 Testing port {port}:")
        print("-" * 30)
        
        base_url = f"http://localhost:{port}"
        
        async with httpx.AsyncClient(timeout=5.0) as client:
            # Test health endpoint
            try:
                response = await client.get(f"{base_url}/health")
                print(f"✅ Health endpoint: {response.status_code}")
                if response.status_code == 200:
                    print(f"   Response: {response.text[:100]}")
            except Exception as e:
                print(f"❌ Health endpoint failed: {str(e)[:50]}")
            
            # Test buildings endpoint
            try:
                response = await client.get(f"{base_url}/api/configuration/buildings")
                print(f"✅ Buildings endpoint: {response.status_code}")
                if response.status_code == 200:
                    data = response.json()
                    print(f"   Buildings found: {len(data.get('data', []))}")
            except Exception as e:
                print(f"❌ Buildings endpoint failed: {str(e)[:50]}")
            
            # Test if update endpoint exists (should return 405 Method Not Allowed for GET)
            try:
                response = await client.get(f"{base_url}/api/configuration/buildings/1")
                print(f"✅ Building detail endpoint: {response.status_code}")
            except Exception as e:
                print(f"❌ Building detail failed: {str(e)[:50]}")
            
            # Test PUT method (should return 405 or 400, not 404)
            try:
                response = await client.put(f"{base_url}/api/configuration/buildings/1", json={"test": "data"})
                print(f"✅ PUT endpoint exists: {response.status_code} (405=method exists)")
            except Exception as e:
                print(f"❌ PUT test failed: {str(e)[:50]}")

if __name__ == '__main__':
    asyncio.run(test_api_endpoints())
