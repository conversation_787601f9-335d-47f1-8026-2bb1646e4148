#!/usr/bin/env python3
"""
Test the updated function registry
"""

import asyncio
from llm_service import IPUFunctionRegistry, create_llm_chatbot
from ipu_client import create_client

async def test_function_registry():
    """Test the function registry"""
    print("🧪 Testing Function Registry")
    print("=" * 50)
    
    try:
        # Create client and registry
        client = create_client()
        registry = IPUFunctionRegistry(client)
        
        print("📋 Available functions:")
        for func_name in sorted(registry.functions.keys()):
            print(f"  ✅ {func_name}")
        
        # Test if update functions are available
        update_functions = [
            "update_building_by_name",
            "update_room_by_name", 
            "update_ward_by_name"
        ]
        
        print(f"\n🔍 Checking for update functions:")
        for func in update_functions:
            if func in registry.functions:
                print(f"  ✅ {func} - AVAILABLE")
            else:
                print(f"  ❌ {func} - MISSING")
        
        # Test actual update function
        print(f"\n🧪 Testing room update function:")
        try:
            result = await registry.execute_function("update_room_by_name", {
                "room_name": "@@#$",
                "new_name": "roomone"
            })
            print(f"  Result: {result.get('success', False)}")
            if not result.get('success'):
                print(f"  Error: {result.get('error', 'Unknown error')}")
        except Exception as e:
            print(f"  Exception: {e}")
        
        # Test chatbot integration
        print(f"\n🤖 Testing chatbot integration:")
        chatbot = create_llm_chatbot('mock', client)
        result = await chatbot.process_message('test-session', 'update room name @@#$ to roomone')
        print(f"  Response type: {result.get('type')}")
        print(f"  Response preview: {result.get('content', '')[:100]}...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_function_registry())
