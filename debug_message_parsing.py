#!/usr/bin/env python3
"""
Debug message parsing to see which update handler is being called
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def debug_message_parsing():
    """Debug message parsing"""
    print("🔍 Debugging Message Parsing")
    print("=" * 50)
    
    try:
        # Create fresh instances
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Test different message formats
        test_messages = [
            "update ward name building_1 to UpdatedWard",
            "update building name demo111 to UpdatedBuilding",
            "update room name Room 1 to UpdatedRoom",
            "change ward building_1 to NewWard",
            "modify ward name Grey ED to Emergency Department"
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n{i}️⃣ Testing message: '{message}'")
            
            # Check what keywords are detected
            user_lower = message.lower()
            
            print(f"   Lowercase: '{user_lower}'")
            
            # Check update detection
            has_update = any(keyword in user_lower for keyword in ["update", "edit", "change", "modify", "rename"])
            print(f"   Has update keyword: {has_update}")
            
            if has_update:
                has_building = any(keyword in user_lower for keyword in ["building", "buildings"])
                has_ward = any(keyword in user_lower for keyword in ["ward", "wards"])
                has_room = any(keyword in user_lower for keyword in ["room", "rooms"])
                
                print(f"   Has building: {has_building}")
                print(f"   Has ward: {has_ward}")
                print(f"   Has room: {has_room}")
                
                # Determine which handler should be called
                if has_building:
                    print(f"   → Should call: _handle_building_update")
                elif has_ward:
                    print(f"   → Should call: _handle_ward_update")
                elif has_room:
                    print(f"   → Should call: _handle_room_update")
                else:
                    print(f"   → Should call: _generic_error_response")
            
            # Test with actual chatbot (but with timeout to avoid long waits)
            print(f"   Testing with chatbot...")
            try:
                result = await asyncio.wait_for(
                    chatbot.process_message('test-session', message),
                    timeout=3.0
                )
                print(f"   Result type: {result.get('type')}")
                content = result.get('content', '')
                if 'Ward Updated Successfully' in content:
                    print(f"   ✅ Ward update detected correctly")
                elif 'Building Updated Successfully' in content:
                    print(f"   ✅ Building update detected correctly")
                elif 'Room Updated Successfully' in content:
                    print(f"   ✅ Room update detected correctly")
                elif 'Update Failed' in content:
                    print(f"   ❌ Update failed (but detected correctly)")
                    # Check which endpoint was called
                    if 'buildings/update-by-name' in content:
                        print(f"   ❌ Called building endpoint instead of ward!")
                    elif 'wards/update-by-name' in content:
                        print(f"   ✅ Called ward endpoint correctly")
                    elif 'rooms/update-by-name' in content:
                        print(f"   ✅ Called room endpoint correctly")
                else:
                    print(f"   ❓ Unexpected response: {content[:100]}...")
                    
            except asyncio.TimeoutError:
                print(f"   ⏱️ Timeout (API call in progress)")
            except Exception as e:
                print(f"   ❌ Error: {str(e)[:100]}")
        
        print(f"\n✅ Message parsing debug completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(debug_message_parsing())
