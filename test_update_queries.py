#!/usr/bin/env python3
"""
Test update queries handling
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_update_queries():
    """Test different update queries"""
    print("🧪 Testing Update Query Handling")
    print("=" * 50)
    
    try:
        # Create client and chatbot
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Test update queries
        update_queries = [
            "update building name test333 to Buildingone",
            "change building test333 to Buildingone", 
            "rename building test333 to Buildingone",
            "edit building name test333 to Buildingone",
            "modify building test333 to Buildingone",
            "update room name Room1 to NewRoom",
            "update building",  # Invalid format
            "update something else"  # Generic error
        ]
        
        for i, query in enumerate(update_queries, 1):
            print(f"\n{i}️⃣ Testing: '{query}'")
            print("-" * 40)
            
            result = await chatbot.process_message('test-session', query)
            
            print(f"Type: {result.get('type')}")
            print("Response:")
            print(result.get('content', 'No content'))
            print()
        
        print("✅ Update query tests completed!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")

if __name__ == '__main__':
    asyncio.run(test_update_queries())
