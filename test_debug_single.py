#!/usr/bin/env python3
"""
Test single query with debug output
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_debug_single():
    """Test single query with debug output"""
    print("🧪 Testing Single Query with Debug")
    print("=" * 60)
    
    try:
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        query = "rooms in Surgical Ward"
        print(f"Testing: '{query}'")
        
        result = await asyncio.wait_for(
            chatbot.process_message('test-session', query),
            timeout=25.0
        )
        
        print(f"Result type: {result.get('type')}")
        content = result.get('content', '')
        
        # Show first few lines of response
        lines = content.split('\n')[:5]
        for line in lines:
            if line.strip():
                print(f"Response: {line.strip()}")
        
        print(f"\n✅ Debug test completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_debug_single())
