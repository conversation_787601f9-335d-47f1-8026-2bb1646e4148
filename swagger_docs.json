{"openapi": "3.0.4", "info": {"title": "IPU Configuration API", "description": "API for managing IPU Buildings, Wards, and Rooms using unified tblLocality table", "version": "v1"}, "paths": {"/api/Configuration/buildings": {"get": {"tags": ["Configuration"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoListApiResponse"}}}}}}, "post": {"tags": ["Configuration"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateLocalityDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateLocalityDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateLocalityDto"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}}}}, "/api/Configuration/buildings/{id}": {"get": {"tags": ["Configuration"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}}}, "put": {"tags": ["Configuration"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLocalityDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateLocalityDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateLocalityDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}}}, "delete": {"tags": ["Configuration"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}}}}, "/api/Configuration/wards": {"get": {"tags": ["Configuration"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoListApiResponse"}}}}}}}, "/api/Configuration/buildings/{buildingId}/wards": {"get": {"tags": ["Configuration"], "parameters": [{"name": "buildingId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoListApiResponse"}}}}}}}, "/api/Configuration/rooms": {"get": {"tags": ["Configuration"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoListApiResponse"}}}}}}}, "/api/Configuration/rooms/available": {"get": {"tags": ["Configuration"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoListApiResponse"}}}}}}}, "/api/Configuration/wards/{wardId}/rooms": {"get": {"tags": ["Configuration"], "parameters": [{"name": "wardId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoListApiResponse"}}}}}}}, "/api/Configuration/buildings/search": {"get": {"tags": ["Configuration"], "parameters": [{"name": "searchTerm", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoListApiResponse"}}}}}}}, "/api/Configuration/wards/search": {"get": {"tags": ["Configuration"], "parameters": [{"name": "searchTerm", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoListApiResponse"}}}}}}}, "/api/Configuration/rooms/search": {"get": {"tags": ["Configuration"], "parameters": [{"name": "searchTerm", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoListApiResponse"}}}}}}}, "/api/Configuration/search": {"get": {"tags": ["Configuration"], "parameters": [{"name": "searchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "localityTypeId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoListApiResponse"}}}}}}}, "/api/Configuration/buildings/update-by-name/{buildingName}": {"put": {"tags": ["Configuration"], "parameters": [{"name": "buildingName", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLocalityDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateLocalityDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateLocalityDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}}}}, "/api/Configuration/wards/update-by-name/{wardName}": {"put": {"tags": ["Configuration"], "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLocalityDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateLocalityDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateLocalityDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}}}}, "/api/Configuration/rooms/update-by-name/{roomName}": {"put": {"tags": ["Configuration"], "parameters": [{"name": "roomName", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLocalityDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateLocalityDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateLocalityDto"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}}}}, "/api/Configuration/wards/delete-by-name/{wardName}": {"delete": {"tags": ["Configuration"], "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}}}}, "/api/Configuration/rooms/delete-by-name/{roomName}": {"delete": {"tags": ["Configuration"], "parameters": [{"name": "roomName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}}}}, "/api/Configuration/buildings/simple": {"post": {"tags": ["Configuration"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimpleCreateLocalityDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SimpleCreateLocalityDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SimpleCreateLocalityDto"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}}}}, "/api/Configuration/wards/simple": {"post": {"tags": ["Configuration"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimpleCreateLocalityDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SimpleCreateLocalityDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SimpleCreateLocalityDto"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}}}}, "/api/Configuration/rooms/simple": {"post": {"tags": ["Configuration"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimpleCreateLocalityDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SimpleCreateLocalityDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SimpleCreateLocalityDto"}}}}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalityDtoApiResponse"}}}}}}}, "/health": {"get": {"tags": ["IPU.API"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringDateTimeStringString<>f__AnonymousType1"}}}}}}}, "/test": {"get": {"tags": ["IPU.API"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringDateTimeString<>f__AnonymousType2"}}}}}}}}, "components": {"schemas": {"BooleanApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "boolean"}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "CreateLocalityDto": {"type": "object", "properties": {"localityTitle": {"type": "string", "nullable": true}, "localityCode": {"type": "string", "nullable": true}, "localityDescription": {"type": "string", "nullable": true}, "localityTypeID": {"type": "integer", "format": "int32"}, "parentLocalityID": {"type": "integer", "format": "int32", "nullable": true}, "facilities": {"type": "string", "nullable": true}, "isMultipleRoom": {"type": "boolean", "nullable": true}, "isHomeLeave": {"type": "boolean", "nullable": true}, "sequenceNo": {"type": "integer", "format": "int32", "nullable": true}, "practiceID": {"type": "integer", "format": "int32", "nullable": true}, "practiceLocationID": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "LocalityDto": {"type": "object", "properties": {"localityID": {"type": "integer", "format": "int32"}, "localityTitle": {"type": "string", "nullable": true}, "localityCode": {"type": "string", "nullable": true}, "localityDescription": {"type": "string", "nullable": true}, "localityTypeID": {"type": "integer", "format": "int32"}, "localityTypeName": {"type": "string", "nullable": true}, "parentLocalityID": {"type": "integer", "format": "int32", "nullable": true}, "parentLocalityTitle": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isOccupied": {"type": "boolean"}, "facilities": {"type": "string", "nullable": true}, "isMultipleRoom": {"type": "boolean", "nullable": true}, "isHomeLeave": {"type": "boolean", "nullable": true}, "sequenceNo": {"type": "integer", "format": "int32", "nullable": true}, "insertedAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "childCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "LocalityDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/LocalityDto"}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "LocalityDtoListApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/LocalityDto"}, "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "SimpleCreateLocalityDto": {"type": "object", "properties": {"localityTitle": {"type": "string", "nullable": true}, "parentLocalityName": {"type": "string", "nullable": true}, "localityDescription": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringDateTimeString<>f__AnonymousType2": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StringDateTimeStringString<>f__AnonymousType1": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "environment": {"type": "string", "nullable": true}, "version": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateLocalityDto": {"type": "object", "properties": {"localityTitle": {"type": "string", "nullable": true}, "localityCode": {"type": "string", "nullable": true}, "localityDescription": {"type": "string", "nullable": true}, "facilities": {"type": "string", "nullable": true}, "isMultipleRoom": {"type": "boolean", "nullable": true}, "isHomeLeave": {"type": "boolean", "nullable": true}, "sequenceNo": {"type": "integer", "format": "int32", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}}}}