#!/usr/bin/env python3
"""
Test API status and existing data
"""

import asyncio
from ipu_client import create_client

async def test_api_status():
    """Test API status and existing data"""
    print("🔍 Testing API Status and Existing Data")
    print("=" * 60)
    
    try:
        client = create_client()
        
        # Test basic connectivity
        print("1️⃣ Testing basic connectivity:")
        try:
            buildings = await client.get_buildings()
            print(f"✅ API is working - Found {len(buildings)} buildings")
            
            # Show first few buildings
            for building in buildings[:3]:
                print(f"   - '{building.get('localityTitle')}' (ID: {building.get('localityID')})")
                
        except Exception as e:
            print(f"❌ API connectivity failed: {e}")
            return
        
        # Test rooms
        print(f"\n2️⃣ Testing rooms:")
        try:
            rooms = await client.get_rooms()
            print(f"✅ Found {len(rooms)} rooms")
            
            # Show first few rooms
            for room in rooms[:3]:
                print(f"   - '{room.get('localityTitle')}' (ID: {room.get('localityID')})")
                
        except Exception as e:
            print(f"❌ Rooms query failed: {e}")
        
        # Test wards
        print(f"\n3️⃣ Testing wards:")
        try:
            wards = await client.get_wards()
            print(f"✅ Found {len(wards)} wards")
            
            # Show first few wards
            for ward in wards[:3]:
                print(f"   - '{ward.get('localityTitle')}' (ID: {ward.get('localityID')})")
                
        except Exception as e:
            print(f"❌ Wards query failed: {e}")
        
        # Test delete with existing room (if any)
        if rooms:
            test_room = rooms[0].get('localityTitle')
            print(f"\n4️⃣ Testing delete with existing room '{test_room}':")
            try:
                result = await client.delete_room_by_name(test_room)
                print(f"Delete result: {result}")
            except Exception as e:
                print(f"❌ Delete test failed: {e}")
        
        # Test create building simple
        print(f"\n5️⃣ Testing create building simple:")
        try:
            building_data = {
                "localityTitle": "APITestBuilding",
                "localityCode": "ATB123",
                "localityDescription": "API test building",
                "isActive": True
            }
            result = await client.create_building_simple(building_data)
            print(f"Create result: {result}")
        except Exception as e:
            print(f"❌ Create building test failed: {e}")
        
        print(f"\n✅ API status test completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_api_status())
