#!/usr/bin/env python3
"""
Indici Assistant - Integrated LLM Web Application

This replaces the existing chatbot with a professional LLM-powered version
while maintaining the existing Indici interface design.
"""

import asyncio
import json
import logging
import os
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

from flask import Flask, render_template, request, jsonify, session
from flask_socketio import SocketIO, emit, join_room, leave_room
from flask_cors import CORS

# Import our modules
from config import config, validate_config
from ipu_client import create_client, IPUAPIError
from llm_service import create_llm_chatbot, LLMProvider

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'indici-assistant-secret-key')
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')
CORS(app)

# Global variables
llm_chatbot = None
ipu_client = None


class SessionManager:
    """Manage user sessions and conversations"""
    
    def __init__(self):
        self.sessions = {}
    
    def create_session(self, socket_id: str) -> str:
        """Create new session"""
        session_id = str(uuid.uuid4())
        self.sessions[session_id] = {
            'socket_id': socket_id,
            'created_at': datetime.now(),
            'last_activity': datetime.now(),
            'message_count': 0,
            'user_info': {}
        }
        return session_id
    
    def get_session(self, session_id: str) -> Optional[Dict]:
        """Get session info"""
        return self.sessions.get(session_id)
    
    def update_activity(self, session_id: str):
        """Update last activity"""
        if session_id in self.sessions:
            self.sessions[session_id]['last_activity'] = datetime.now()
            self.sessions[session_id]['message_count'] += 1
    
    def remove_session(self, session_id: str):
        """Remove session"""
        if session_id in self.sessions:
            del self.sessions[session_id]


session_manager = SessionManager()


async def setup_app():
    """Setup the application with LLM and IPU client"""
    global llm_chatbot, ipu_client
    
    try:
        # Validate configuration
        if not validate_config():
            logger.error("Configuration validation failed")
            return False
        
        # Create IPU client
        ipu_client = create_client()
        
        # Test IPU connection
        try:
            health_ok = await ipu_client.health_check()
            if health_ok:
                logger.info("✅ IPU-API connection successful")
            else:
                logger.warning("⚠️ IPU-API health check failed - continuing with limited functionality")
        except Exception as e:
            logger.warning(f"⚠️ IPU-API not available: {e} - continuing with demo mode")
        
        # Determine LLM provider based on available API keys and preferences
        llm_provider = "mock"  # Default fallback
        
        # Check for Hugging Face first (if user prefers local models)
        if os.getenv("USE_HUGGINGFACE", "false").lower() == "true":
            llm_provider = "huggingface"
        # Then check for API keys
        elif os.getenv("OPENAI_API_KEY"):
            llm_provider = "openai"
        elif os.getenv("ANTHROPIC_API_KEY"):
            llm_provider = "anthropic"
        
        # Create LLM chatbot
        llm_chatbot = create_llm_chatbot(llm_provider, ipu_client)
        logger.info(f"✅ LLM chatbot initialized with provider: {llm_provider}")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to setup application: {e}")
        return False


@app.route('/')
def index():
    """Main Indici Assistant interface"""
    return render_template('indici_chat.html')


@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'llm_available': llm_chatbot is not None,
        'ipu_api_available': ipu_client is not None,
        'active_sessions': len(session_manager.sessions)
    })


@app.route('/api/session/info')
def session_info():
    """Get session information"""
    session_id = request.args.get('session_id')
    if not session_id:
        return jsonify({'error': 'Session ID required'}), 400
    
    session_data = session_manager.get_session(session_id)
    if not session_data:
        return jsonify({'error': 'Session not found'}), 404
    
    # Get conversation summary from LLM chatbot
    conversation_summary = {}
    if llm_chatbot:
        conversation_summary = llm_chatbot.get_conversation_summary(session_id)
    
    return jsonify({
        'session': session_data,
        'conversation': conversation_summary
    })


@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    socket_id = request.sid
    session_id = session_manager.create_session(socket_id)
    
    # Join session room
    join_room(session_id)
    
    # Store session in Flask session
    session['session_id'] = session_id
    
    # Determine LLM provider for status
    provider_status = "Mock LLM"
    if llm_chatbot:
        if llm_chatbot.provider.value == "openai":
            provider_status = "OpenAI GPT-4"
        elif llm_chatbot.provider.value == "anthropic":
            provider_status = "Anthropic Claude"
        elif llm_chatbot.provider.value == "huggingface":
            provider_status = "Hugging Face Model"
    
    emit('connected', {
        'message': f'Connected to Indici Assistant! 🏥 Using {provider_status}',
        'session_id': session_id,
        'llm_available': llm_chatbot is not None,
        'ipu_api_available': ipu_client is not None,
        'provider': provider_status
    })
    
    logger.info(f"Client connected: {socket_id} -> Session: {session_id}")


@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    socket_id = request.sid
    session_id = session.get('session_id')
    
    if session_id:
        leave_room(session_id)
        logger.info(f"Client disconnected: {socket_id} -> Session: {session_id}")


@socketio.on('message')
def handle_message(data):
    """Handle incoming chat message"""
    session_id = session.get('session_id')
    message = data.get('message', '').strip()
    
    if not session_id:
        emit('error', {'message': 'No active session'})
        return
    
    if not message:
        emit('response', {
            'type': 'error',
            'message': 'Please enter a message.'
        })
        return
    
    # Update session activity
    session_manager.update_activity(session_id)
    
    # Process message with LLM
    if llm_chatbot:
        # Run async function in thread
        def process_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                response = loop.run_until_complete(
                    llm_chatbot.process_message(session_id, message)
                )
                
                # Emit response
                socketio.emit('response', {
                    'type': response.get('type', 'success'),
                    'message': response.get('content', response.get('error', 'No response')),
                    'timestamp': datetime.now().isoformat(),
                    'function_call': response.get('function_call'),
                    'session_id': session_id
                }, room=session_id)
                
            except Exception as e:
                logger.error(f"Error processing message: {e}")
                socketio.emit('response', {
                    'type': 'error',
                    'message': f'Sorry, I encountered an error: {str(e)}',
                    'timestamp': datetime.now().isoformat(),
                    'session_id': session_id
                }, room=session_id)
            finally:
                loop.close()
        
        # Run in background thread
        socketio.start_background_task(process_async)
    else:
        emit('response', {
            'type': 'error',
            'message': 'LLM service not available',
            'timestamp': datetime.now().isoformat()
        })


@socketio.on('clear_conversation')
def handle_clear_conversation():
    """Clear conversation history"""
    session_id = session.get('session_id')
    
    if not session_id:
        emit('error', {'message': 'No active session'})
        return
    
    if llm_chatbot:
        success = llm_chatbot.clear_conversation(session_id)
        if success:
            emit('conversation_cleared', {
                'message': 'Conversation history cleared',
                'timestamp': datetime.now().isoformat()
            })
        else:
            emit('error', {'message': 'Failed to clear conversation'})
    else:
        emit('error', {'message': 'LLM service not available'})


def run_app():
    """Run the Indici Assistant application"""
    print("🏥 Indici Assistant - Professional LLM Integration")
    print("=" * 60)
    print("🚀 Starting integrated web server...")
    print("📱 Web Interface: http://localhost:5000")
    print("🔗 Health Check: http://localhost:5000/api/health")
    print("\n🤖 LLM Provider Priority:")
    print("   1. Hugging Face (if USE_HUGGINGFACE=true)")
    print("   2. OpenAI (if OPENAI_API_KEY set)")
    print("   3. Anthropic (if ANTHROPIC_API_KEY set)")
    print("   4. Mock LLM (fallback)")
    print("\n💡 Environment Variables:")
    print(f"   • USE_HUGGINGFACE: {os.getenv('USE_HUGGINGFACE', 'false')}")
    print(f"   • HUGGINGFACE_MODEL: {os.getenv('HUGGINGFACE_MODEL', 'microsoft/DialoGPT-medium')}")
    print(f"   • OpenAI: {'✅ Available' if os.getenv('OPENAI_API_KEY') else '❌ Not set'}")
    print(f"   • Anthropic: {'✅ Available' if os.getenv('ANTHROPIC_API_KEY') else '❌ Not set'}")
    print("\n🛑 Press Ctrl+C to stop")
    print("=" * 60)
    
    try:
        # Setup application
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        setup_success = loop.run_until_complete(setup_app())
        if setup_success:
            logger.info("✅ Application setup completed successfully")
        else:
            logger.warning("⚠️ Application setup had issues - running with limited functionality")
        
        # Start the web server
        socketio.run(
            app, 
            host='0.0.0.0', 
            port=5000, 
            debug=False,
            allow_unsafe_werkzeug=True
        )
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Failed to start application: {e}")
    finally:
        if 'loop' in locals():
            loop.close()


if __name__ == '__main__':
    run_app()
