#!/usr/bin/env python3
"""
API Connection Test Script

Tests all API connections and displays results in formatted text and tables.
"""

import asyncio
import os
import sys
from datetime import datetime
from tabulate import tabulate

# Import our modules
try:
    from ipu_client import create_client
    from llm_service import create_llm_chatbot, LLMProvider
    from config import config
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Make sure all dependencies are installed: pip install -r requirements.txt")
    sys.exit(1)


def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 80)
    print(f"🔍 {title}")
    print("=" * 80)


def print_section(title):
    """Print a formatted section"""
    print(f"\n📋 {title}")
    print("-" * 60)


async def test_ipu_api():
    """Test IPU-API connection and functionality"""
    print_section("IPU-API Connection Test")
    
    results = []
    
    try:
        # Create client
        client = create_client()
        print("✅ IPU Client created successfully")
        
        # Test health check
        try:
            health = await client.health_check()
            results.append(["Health Check", "✅ PASS" if health else "❌ FAIL", str(health)])
            print(f"   Health Check: {'✅ PASS' if health else '❌ FAIL'}")
        except Exception as e:
            results.append(["Health Check", "❌ ERROR", str(e)])
            print(f"   Health Check: ❌ ERROR - {e}")
        
        # Test get buildings
        try:
            buildings = await client.get_buildings()
            if buildings and len(buildings) > 0:
                results.append(["Get Buildings", "✅ PASS", f"{len(buildings)} buildings found"])
                print(f"   Get Buildings: ✅ PASS - {len(buildings)} buildings found")
                
                # Show buildings table
                if buildings:
                    building_data = []
                    for building in buildings[:5]:  # Show first 5
                        building_data.append([
                            building.get('id', 'N/A'),
                            building.get('name', 'N/A'),
                            building.get('code', 'N/A'),
                            building.get('floors', 'N/A')
                        ])
                    
                    print("\n📊 Buildings Sample:")
                    print(tabulate(building_data, 
                                 headers=['ID', 'Name', 'Code', 'Floors'],
                                 tablefmt='grid'))
            else:
                results.append(["Get Buildings", "⚠️ EMPTY", "No buildings returned"])
                print(f"   Get Buildings: ⚠️ EMPTY - No buildings returned")
        except Exception as e:
            results.append(["Get Buildings", "❌ ERROR", str(e)])
            print(f"   Get Buildings: ❌ ERROR - {e}")
        
        # Test get rooms
        try:
            rooms = await client.get_rooms()
            if rooms and len(rooms) > 0:
                results.append(["Get Rooms", "✅ PASS", f"{len(rooms)} rooms found"])
                print(f"   Get Rooms: ✅ PASS - {len(rooms)} rooms found")
                
                # Show rooms table
                if rooms:
                    room_data = []
                    for room in rooms[:5]:  # Show first 5
                        room_data.append([
                            room.get('id', 'N/A'),
                            room.get('room_number', 'N/A'),
                            room.get('room_type', 'N/A'),
                            room.get('status', 'N/A'),
                            room.get('building_name', 'N/A')
                        ])
                    
                    print("\n📊 Rooms Sample:")
                    print(tabulate(room_data, 
                                 headers=['ID', 'Room #', 'Type', 'Status', 'Building'],
                                 tablefmt='grid'))
            else:
                results.append(["Get Rooms", "⚠️ EMPTY", "No rooms returned"])
                print(f"   Get Rooms: ⚠️ EMPTY - No rooms returned")
        except Exception as e:
            results.append(["Get Rooms", "❌ ERROR", str(e)])
            print(f"   Get Rooms: ❌ ERROR - {e}")
        
        # Test analytics
        try:
            analytics = await client.get_analytics()
            if analytics:
                results.append(["Get Analytics", "✅ PASS", "Analytics data retrieved"])
                print(f"   Get Analytics: ✅ PASS - Analytics data retrieved")
                
                # Show analytics table
                analytics_data = [
                    ["Total Buildings", analytics.get('total_buildings', 'N/A')],
                    ["Total Rooms", analytics.get('total_rooms', 'N/A')],
                    ["Occupied Rooms", analytics.get('occupied_rooms', 'N/A')],
                    ["Available Rooms", analytics.get('available_rooms', 'N/A')],
                    ["Occupancy Rate", f"{analytics.get('occupancy_rate', 0):.1f}%"],
                    ["Last Updated", analytics.get('last_updated', 'N/A')]
                ]
                
                print("\n📊 Analytics Summary:")
                print(tabulate(analytics_data, 
                             headers=['Metric', 'Value'],
                             tablefmt='grid'))
            else:
                results.append(["Get Analytics", "⚠️ EMPTY", "No analytics returned"])
                print(f"   Get Analytics: ⚠️ EMPTY - No analytics returned")
        except Exception as e:
            results.append(["Get Analytics", "❌ ERROR", str(e)])
            print(f"   Get Analytics: ❌ ERROR - {e}")
        
    except Exception as e:
        results.append(["IPU Client", "❌ CRITICAL", str(e)])
        print(f"❌ Critical Error: {e}")
    
    # Summary table
    print(f"\n📊 IPU-API Test Results:")
    print(tabulate(results, 
                   headers=['Test', 'Status', 'Details'],
                   tablefmt='grid'))
    
    return results


async def test_llm_service():
    """Test LLM service functionality"""
    print_section("LLM Service Test")
    
    results = []
    
    # Test different providers
    providers_to_test = [
        ("mock", "Mock LLM"),
        ("openai", "OpenAI GPT-4"),
        ("anthropic", "Anthropic Claude"),
        ("huggingface", "Hugging Face")
    ]
    
    ipu_client = create_client()
    
    for provider_key, provider_name in providers_to_test:
        try:
            print(f"\n🤖 Testing {provider_name}...")
            
            # Create chatbot
            chatbot = create_llm_chatbot(provider_key, ipu_client)
            
            # Test basic message
            test_message = "Hello, can you help me with IPU management?"
            response = await chatbot.process_message("test-session", test_message)
            
            if response and response.get('content'):
                status = "✅ PASS"
                details = f"Response length: {len(response['content'])} chars"
                print(f"   {provider_name}: ✅ PASS")
                
                # Show sample response (truncated)
                sample_response = response['content'][:200] + "..." if len(response['content']) > 200 else response['content']
                print(f"   Sample Response: {sample_response}")
                
            else:
                status = "❌ FAIL"
                details = "No response content"
                print(f"   {provider_name}: ❌ FAIL - No response content")
            
            results.append([provider_name, status, details])
            
        except Exception as e:
            results.append([provider_name, "❌ ERROR", str(e)])
            print(f"   {provider_name}: ❌ ERROR - {e}")
    
    # Summary table
    print(f"\n📊 LLM Service Test Results:")
    print(tabulate(results, 
                   headers=['Provider', 'Status', 'Details'],
                   tablefmt='grid'))
    
    return results


def test_environment():
    """Test environment configuration"""
    print_section("Environment Configuration")
    
    env_data = [
        ["USE_HUGGINGFACE", os.getenv('USE_HUGGINGFACE', 'false')],
        ["HUGGINGFACE_MODEL", os.getenv('HUGGINGFACE_MODEL', 'microsoft/DialoGPT-medium')],
        ["OPENAI_API_KEY", "✅ Set" if os.getenv('OPENAI_API_KEY') else "❌ Not set"],
        ["ANTHROPIC_API_KEY", "✅ Set" if os.getenv('ANTHROPIC_API_KEY') else "❌ Not set"],
        ["IPU_API_BASE_URL", getattr(config, 'IPU_API_BASE_URL', 'http://localhost:5077')],
        ["IPU_API_TIMEOUT", str(getattr(config, 'IPU_API_TIMEOUT', 30))],
    ]
    
    print("📊 Environment Variables:")
    print(tabulate(env_data, 
                   headers=['Variable', 'Value'],
                   tablefmt='grid'))
    
    return env_data


async def main():
    """Main test function"""
    print_header("API CONNECTION AND FUNCTIONALITY TEST")
    print(f"🕒 Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test environment
    env_results = test_environment()
    
    # Test IPU-API
    ipu_results = await test_ipu_api()
    
    # Test LLM Service
    llm_results = await test_llm_service()
    
    # Overall summary
    print_header("OVERALL TEST SUMMARY")
    
    # Count results
    ipu_pass = sum(1 for r in ipu_results if "✅ PASS" in r[1])
    ipu_total = len(ipu_results)
    
    llm_pass = sum(1 for r in llm_results if "✅ PASS" in r[1])
    llm_total = len(llm_results)
    
    summary_data = [
        ["IPU-API Tests", f"{ipu_pass}/{ipu_total}", "✅ GOOD" if ipu_pass > 0 else "❌ ISSUES"],
        ["LLM Service Tests", f"{llm_pass}/{llm_total}", "✅ GOOD" if llm_pass > 0 else "❌ ISSUES"],
        ["Environment Config", "✅ LOADED", "✅ GOOD"],
        ["Overall Status", "", "✅ READY" if (ipu_pass > 0 and llm_pass > 0) else "⚠️ PARTIAL"]
    ]
    
    print("📊 Final Summary:")
    print(tabulate(summary_data, 
                   headers=['Component', 'Results', 'Status'],
                   tablefmt='grid'))
    
    # Recommendations
    print("\n💡 Recommendations:")
    if ipu_pass == 0:
        print("   • Check IPU-API server is running on http://localhost:5077")
        print("   • Verify IPU-API configuration in config.py")
    
    if llm_pass <= 1:  # Only mock working
        print("   • Set up LLM provider:")
        print("     - Hugging Face: set USE_HUGGINGFACE=true")
        print("     - OpenAI: set OPENAI_API_KEY=your_key")
        print("     - Anthropic: set ANTHROPIC_API_KEY=your_key")
    
    print("\n🚀 To start the application:")
    print("   python start_web.py")
    print("   Then open: http://localhost:5000")


if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
