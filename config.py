"""
Configuration settings for IPU MCP Chatbot
"""

import os
from typing import Optional
from dataclasses import dataclass


@dataclass
class IPUConfig:
    """IPU API configuration"""
    base_url: str = "http://localhost:5077"
    timeout: int = 30
    max_retries: int = 3


@dataclass
class MCPConfig:
    """MCP server configuration"""
    server_name: str = "ipu-management-server"
    server_version: str = "1.0.0"
    protocol_version: str = "2024-11-05"


@dataclass
class ChatbotConfig:
    """Chatbot configuration"""
    welcome_message: str = "🏥 Welcome to IPU Management Assistant! Ask me about rooms, wards, buildings, or occupancy."
    prompt_prefix: str = "IPU> "
    max_history: int = 100
    show_json: bool = False
    auto_format: bool = True


@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: Optional[str] = None


class Config:
    """Main configuration class"""
    
    def __init__(self):
        self.ipu = IPUConfig(
            base_url=os.getenv("IPU_API_URL", "http://localhost:5077"),
            timeout=int(os.getenv("IPU_API_TIMEOUT", "30")),
            max_retries=int(os.getenv("IPU_API_MAX_RETRIES", "3"))
        )
        
        self.mcp = MCPConfig(
            server_name=os.getenv("MCP_SERVER_NAME", "ipu-management-server"),
            server_version=os.getenv("MCP_SERVER_VERSION", "1.0.0"),
            protocol_version=os.getenv("MCP_PROTOCOL_VERSION", "2024-11-05")
        )
        
        self.chatbot = ChatbotConfig(
            welcome_message=os.getenv("CHATBOT_WELCOME", 
                "🏥 Welcome to IPU Management Assistant! Ask me about rooms, wards, buildings, or occupancy."),
            prompt_prefix=os.getenv("CHATBOT_PROMPT", "IPU> "),
            max_history=int(os.getenv("CHATBOT_MAX_HISTORY", "100")),
            show_json=os.getenv("CHATBOT_SHOW_JSON", "false").lower() == "true",
            auto_format=os.getenv("CHATBOT_AUTO_FORMAT", "true").lower() == "true"
        )
        
        self.logging = LoggingConfig(
            level=os.getenv("LOG_LEVEL", "INFO"),
            format=os.getenv("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
            file=os.getenv("LOG_FILE")
        )


# Global configuration instance
config = Config()


# Environment-specific configurations
def load_env_config(env_file: str = ".env"):
    """Load configuration from environment file"""
    try:
        from dotenv import load_dotenv
        load_dotenv(env_file)
        return Config()
    except ImportError:
        print("python-dotenv not installed. Using default configuration.")
        return Config()


# Validation functions
def validate_config() -> bool:
    """Validate configuration settings"""
    try:
        # Validate IPU API URL
        if not config.ipu.base_url.startswith(("http://", "https://")):
            print(f"Warning: IPU API URL might be invalid: {config.ipu.base_url}")
        
        # Validate timeout values
        if config.ipu.timeout <= 0:
            print(f"Warning: Invalid timeout value: {config.ipu.timeout}")
            return False
        
        # Validate retry count
        if config.ipu.max_retries < 0:
            print(f"Warning: Invalid retry count: {config.ipu.max_retries}")
            return False
        
        return True
    except Exception as e:
        print(f"Configuration validation error: {e}")
        return False


# Helper functions
def get_api_url(endpoint: str = "") -> str:
    """Get full API URL for endpoint"""
    base = config.ipu.base_url.rstrip('/')
    endpoint = endpoint.lstrip('/')
    return f"{base}/{endpoint}" if endpoint else base


def is_debug_mode() -> bool:
    """Check if debug mode is enabled"""
    return config.logging.level.upper() == "DEBUG"


def get_user_agent() -> str:
    """Get user agent string for API requests"""
    return f"{config.mcp.server_name}/{config.mcp.server_version}"


# Export commonly used values
API_BASE_URL = config.ipu.base_url
MCP_SERVER_NAME = config.mcp.server_name
CHATBOT_WELCOME = config.chatbot.welcome_message
