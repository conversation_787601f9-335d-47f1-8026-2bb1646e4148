# IPU MCP Chatbot Configuration
# Copy this file to .env and customize the values

# IPU API Configuration
IPU_API_URL=http://localhost:5077
IPU_API_TIMEOUT=30
IPU_API_MAX_RETRIES=3

# MCP Server Configuration
MCP_SERVER_NAME=ipu-management-server
MCP_SERVER_VERSION=1.0.0
MCP_PROTOCOL_VERSION=2024-11-05

# Chatbot Configuration
CHATBOT_WELCOME=🏥 Welcome to IPU Management Assistant! Ask me about rooms, wards, buildings, or occupancy.
CHATBOT_PROMPT=IPU> 
CHATBOT_MAX_HISTORY=100
CHATBOT_SHOW_JSON=false
CHATBOT_AUTO_FORMAT=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
# LOG_FILE=ipu_mcp.log  # Uncomment to log to file
