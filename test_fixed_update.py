#!/usr/bin/env python3
"""
Test the fixed update functionality
"""

import asyncio
import httpx
import json
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_fixed_update():
    """Test the fixed update functionality"""
    print("🧪 Testing Fixed Update Functionality")
    print("=" * 50)
    
    # Test 1: Direct API call with correct format
    print("1️⃣ Testing direct API call with isActive field:")
    async with httpx.AsyncClient() as client:
        try:
            # Test the correct format with isActive
            test_data = {
                "localityTitle": "surgicalroom",
                "isActive": True
            }
            
            response = await client.put(
                "http://localhost:5077/api/Configuration/rooms/update-by-name/surg",
                json=test_data,
                timeout=10.0
            )
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ Success! Updated room:")
                print(f"   New name: {result.get('data', {}).get('localityTitle', 'Unknown')}")
            else:
                print(f"   ❌ Response: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Test 2: Using the updated chatbot
    print(f"\n2️⃣ Testing updated chatbot functionality:")
    try:
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Test room update
        print("   Testing: 'update room name surg to surgicalroom'")
        result = await asyncio.wait_for(
            chatbot.process_message('test-session', 'update room name surg to surgicalroom'),
            timeout=15.0
        )
        
        print(f"   Type: {result.get('type')}")
        if result.get('type') == 'success':
            print(f"   ✅ Success!")
            print(f"   Response: {result.get('content', '')[:200]}...")
        else:
            print(f"   ❌ Failed:")
            print(f"   Response: {result.get('content', '')[:300]}...")
            
    except asyncio.TimeoutError:
        print(f"   ⏱️ Timeout - API call took too long")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Verify the update worked
    print(f"\n3️⃣ Verifying the update worked:")
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get("http://localhost:5077/api/Configuration/rooms")
            rooms = response.json()
            
            # Look for the updated room
            updated_room = None
            for room in rooms.get('data', []):
                if room.get('localityTitle') == 'surgicalroom':
                    updated_room = room
                    break
            
            if updated_room:
                print(f"   ✅ Found updated room 'surgicalroom':")
                print(f"   ID: {updated_room.get('localityID')}")
                print(f"   Code: {updated_room.get('localityCode')}")
                print(f"   Ward: {updated_room.get('parentLocalityTitle')}")
            else:
                print(f"   ❌ Updated room 'surgicalroom' not found")
                print("   Available rooms:")
                for room in rooms.get('data', [])[:5]:
                    print(f"     - {room.get('localityTitle', 'Unknown')}")
                    
        except Exception as e:
            print(f"   ❌ Error verifying: {e}")

if __name__ == '__main__':
    asyncio.run(test_fixed_update())
