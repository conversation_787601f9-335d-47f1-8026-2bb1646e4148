#!/usr/bin/env python3
"""
Test the new table formatting for LLM responses
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_buildings_response():
    """Test buildings response with table formatting"""
    print("🧪 Testing Buildings Response with Table Format")
    print("=" * 60)
    
    try:
        # Create client and chatbot
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Test buildings query
        result = await chatbot.process_message('test-session', 'Show me all buildings')
        
        print("📋 Response Type:", result.get('type', 'unknown'))
        print("📋 Function Called:", result.get('function_call', {}).get('name', 'none'))
        print("\n📄 Response Content:")
        print("-" * 40)
        print(result.get('content', 'No content'))
        print("-" * 40)
        
        return result
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

async def test_rooms_response():
    """Test rooms response with table formatting"""
    print("\n🧪 Testing Available Rooms Response with Table Format")
    print("=" * 60)
    
    try:
        # Create client and chatbot
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Test rooms query
        result = await chatbot.process_message('test-session', 'Show me available rooms')
        
        print("📋 Response Type:", result.get('type', 'unknown'))
        print("📋 Function Called:", result.get('function_call', {}).get('name', 'none'))
        print("\n📄 Response Content:")
        print("-" * 40)
        print(result.get('content', 'No content'))
        print("-" * 40)
        
        return result
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

async def test_stats_response():
    """Test occupancy stats response with table formatting"""
    print("\n🧪 Testing Occupancy Stats Response with Table Format")
    print("=" * 60)
    
    try:
        # Create client and chatbot
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Test stats query
        result = await chatbot.process_message('test-session', 'Show me occupancy statistics')
        
        print("📋 Response Type:", result.get('type', 'unknown'))
        print("📋 Function Called:", result.get('function_call', {}).get('name', 'none'))
        print("\n📄 Response Content:")
        print("-" * 40)
        print(result.get('content', 'No content'))
        print("-" * 40)
        
        return result
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

async def main():
    """Main test function"""
    print("🏥 Testing New Table Format for LLM Responses")
    print("=" * 80)
    
    # Test all response types
    buildings_result = await test_buildings_response()
    rooms_result = await test_rooms_response()
    stats_result = await test_stats_response()
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 Test Summary:")
    print(f"   Buildings Response: {'✅ SUCCESS' if buildings_result else '❌ FAILED'}")
    print(f"   Rooms Response: {'✅ SUCCESS' if rooms_result else '❌ FAILED'}")
    print(f"   Stats Response: {'✅ SUCCESS' if stats_result else '❌ FAILED'}")
    
    if buildings_result and 'table' in buildings_result.get('content', '').lower():
        print("\n✅ Table formatting is working!")
    else:
        print("\n⚠️ Table formatting may need adjustment")

if __name__ == '__main__':
    asyncio.run(main())
