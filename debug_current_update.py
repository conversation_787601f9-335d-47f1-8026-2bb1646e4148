#!/usr/bin/env python3
"""
Debug current update functionality
"""

import asyncio
import httpx
import json
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def debug_current_update():
    """Debug current update functionality"""
    print("🔍 Debugging Current Update Functionality")
    print("=" * 60)
    
    try:
        # Get current rooms
        client = create_client()
        rooms = await client.get_rooms()
        
        print("1️⃣ Current rooms in system:")
        for room in rooms:
            print(f"   - '{room.get('localityTitle')}' (ID: {room.get('localityID')}, Code: {room.get('localityCode')})")
        
        if not rooms:
            print("❌ No rooms found")
            return
        
        # Test direct API call first
        print(f"\n2️⃣ Testing direct API call:")
        test_room = "room3"  # This exists according to previous output
        
        async with httpx.AsyncClient() as http_client:
            # Test the exact API call
            update_data = {
                "localityTitle": "UpdatedRoom3",
                "isActive": True,
                "localityCode": "UR3"
            }
            
            print(f"   Updating '{test_room}' with data: {json.dumps(update_data)}")
            
            try:
                response = await http_client.put(
                    f"http://localhost:5077/api/Configuration/rooms/update-by-name/{test_room}",
                    json=update_data,
                    timeout=10.0
                )
                
                print(f"   Status: {response.status_code}")
                print(f"   Response: {response.text[:300]}")
                
                if response.status_code == 200:
                    print(f"   ✅ Direct API call successful!")
                else:
                    print(f"   ❌ Direct API call failed")
                    
            except Exception as e:
                print(f"   ❌ Direct API error: {e}")
        
        # Test chatbot functionality
        print(f"\n3️⃣ Testing chatbot update:")
        chatbot = create_llm_chatbot('mock', client)
        
        test_message = f"update room name {test_room} to UpdatedRoom3"
        print(f"   Message: '{test_message}'")
        
        try:
            result = await asyncio.wait_for(
                chatbot.process_message('test-session', test_message),
                timeout=20.0
            )
            
            print(f"   Result type: {result.get('type')}")
            print(f"   Content: {result.get('content', '')[:400]}...")
            
        except asyncio.TimeoutError:
            print(f"   ⏱️ Chatbot timeout")
        except Exception as e:
            print(f"   ❌ Chatbot error: {e}")
        
        # Check what the current room data looks like
        print(f"\n4️⃣ Checking room data structure:")
        if rooms:
            sample_room = rooms[0]
            print(f"   Sample room data:")
            for key, value in sample_room.items():
                print(f"     {key}: {value}")
        
        print(f"\n✅ Debug completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(debug_current_update())
