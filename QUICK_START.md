# 🚀 **Quick Start Guide - Indici Assistant**

## **Get Started in 3 Steps**

### **Step 1: Choose Your Mode**

#### **🌐 Option A: Full System (with IPU-API)**
```bash
# Start IPU-API server first (in IPU-API directory)
cd ../IPU-API
dotnet run

# Then start Indici Assistant
cd ../IPU-MCP-Chatbot
python start_web.py
```
**Open**: http://localhost:5000

#### **🎮 Option B: Demo Mode (No API needed)**
```bash
python demo_web.py
```
**Open**: http://localhost:5001

---

### **Step 2: Try Sample Queries**

Click any query in the sidebar or type:

#### **🏢 Configuration Management**
- "Show me all buildings"
- "List available rooms"
- "Find isolation rooms"
- "What's the occupancy rate?"

#### **🏥 IPU Operations** (Demo Mode)
- "Show me current inpatients"
- "Admit new patient"
- "Process patient discharge"
- "Approve home leave request"

---

### **Step 3: Explore Features**

#### **💬 Natural Language**
Ask questions naturally:
- "How many beds are available?"
- "Which wards are in building 1?"
- "Show me room details for room 101"

#### **📱 Modern Interface**
- **Sidebar**: Organized sample queries
- **Real-time Chat**: Instant responses
- **Mobile Friendly**: Works on all devices
- **Professional Design**: Healthcare-focused UI

---

## **🔧 Advanced Usage**

### **Claude Desktop Integration**
```bash
# Start MCP server
python mcp_server.py

# Add to claude_desktop_config.json:
{
  "mcpServers": {
    "ipu-management": {
      "command": "python",
      "args": ["path/to/mcp_server.py"]
    }
  }
}
```

### **CLI Interface**
```bash
# Command line chatbot
python chatbot.py

# Run tests
python test_ipu_api_integration.py
```

---

## **🆘 Troubleshooting**

### **"Cannot connect to IPU API"**
- ✅ **Solution**: Use demo mode or start IPU-API server
- **Demo**: `python demo_web.py`
- **API**: Start IPU-API with `dotnet run`

### **"Port already in use"**
- ✅ **Solution**: Change port or kill existing process
- **Web**: Edit port in `start_web.py` or `demo_web.py`
- **Kill**: `taskkill /f /im python.exe` (Windows)

### **"Module not found"**
- ✅ **Solution**: Install dependencies
- **Install**: `pip install -r requirements.txt`

---

## **📚 More Help**

- **Full Documentation**: `README.md`
- **Integration Details**: `IPU_API_INTEGRATION_SUMMARY.md`
- **Complete Report**: `FINAL_INTEGRATION_REPORT.md`
- **Claude Setup**: `docs/claude-integration.md`

---

## **🎯 What You Can Do**

### **✅ Currently Available**
- Browse hospital buildings, wards, and rooms
- Check bed availability and occupancy
- Search facilities by name or type
- Get real-time statistics and analytics
- Use natural language queries
- Access via web interface or Claude Desktop

### **🔮 Coming Soon** (Requires IPU-API Extensions)
- Patient admission and discharge
- Home leave request processing
- Care plan management
- Medication tracking
- Bed assignment to patients

---

**🏥 Ready to transform your IPU management with AI? Start now!** ✨
