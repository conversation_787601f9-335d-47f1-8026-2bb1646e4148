#!/usr/bin/env python3
"""
Professional LLM Service for Indici Assistant

This module provides a sophisticated LLM-powered chatbot service with:
- Multiple LLM provider support (OpenAI, Anthropic)
- Conversation memory and context management
- Healthcare-specific prompting
- Function calling for IPU operations
- Professional healthcare communication
"""

import asyncio
import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
from tabulate import tabulate

# LLM imports (with fallbacks)
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False

try:
    from transformers import pipeline
    import torch
    HUGGINGFACE_AVAILABLE = True
except ImportError:
    HUGGINGFACE_AVAILABLE = False

from config import config

logger = logging.getLogger(__name__)


class LLMProvider(Enum):
    """Available LLM providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    HUGGINGFACE = "huggingface"
    MOCK = "mock"  # For testing without API keys


@dataclass
class Message:
    """Chat message structure"""
    role: str  # system, user, assistant, function
    content: str
    timestamp: datetime
    function_call: Optional[Dict] = None
    function_response: Optional[Dict] = None


@dataclass
class ConversationContext:
    """Conversation context and memory"""
    session_id: str
    messages: List[Message]
    user_preferences: Dict[str, Any]
    current_topic: Optional[str] = None
    last_activity: datetime = None
    
    def __post_init__(self):
        if self.last_activity is None:
            self.last_activity = datetime.now()


class IPUFunctionRegistry:
    """Registry of available IPU management functions"""
    
    def __init__(self, ipu_client):
        self.ipu_client = ipu_client
        self.functions = self._register_functions()
    
    def _register_functions(self) -> Dict[str, Dict]:
        """Register all available IPU functions"""
        return {
            "get_buildings": {
                "name": "get_buildings",
                "description": "Get all hospital buildings in the IPU system",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            "search_buildings": {
                "name": "search_buildings",
                "description": "Search for buildings by name, code, or description",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "search_term": {
                            "type": "string",
                            "description": "Search term for building name, code, or description"
                        }
                    },
                    "required": ["search_term"]
                }
            },
            "get_wards": {
                "name": "get_wards",
                "description": "Get all wards in the IPU system",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            "get_wards_by_building": {
                "name": "get_wards_by_building",
                "description": "Get all wards in a specific building",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "building_id": {
                            "type": "integer",
                            "description": "ID of the building to get wards for"
                        }
                    },
                    "required": ["building_id"]
                }
            },
            "get_rooms": {
                "name": "get_rooms",
                "description": "Get all rooms in the IPU system",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            "get_available_rooms": {
                "name": "get_available_rooms",
                "description": "Get all available (unoccupied) rooms",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            "get_wards_by_building_name": {
                "name": "get_wards_by_building_name",
                "description": "Get all wards in a specific building by building name",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "building_name": {
                            "type": "string",
                            "description": "Name of the building to get wards for"
                        }
                    },
                    "required": ["building_name"]
                }
            },
            "get_rooms_by_ward_name": {
                "name": "get_rooms_by_ward_name",
                "description": "Get all rooms in a specific ward by ward name",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "ward_name": {
                            "type": "string",
                            "description": "Name of the ward to get rooms for"
                        }
                    },
                    "required": ["ward_name"]
                }
            },
            "delete_ward_by_name": {
                "name": "delete_ward_by_name",
                "description": "Delete a ward by name",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "ward_name": {
                            "type": "string",
                            "description": "Name of the ward to delete"
                        }
                    },
                    "required": ["ward_name"]
                }
            },
            "delete_room_by_name": {
                "name": "delete_room_by_name",
                "description": "Delete a room by name",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "room_name": {
                            "type": "string",
                            "description": "Name of the room to delete"
                        }
                    },
                    "required": ["room_name"]
                }
            },
            "create_building_simple": {
                "name": "create_building_simple",
                "description": "Create a new building using simple endpoint",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "building_data": {
                            "type": "object",
                            "description": "Building data including name, code, and description"
                        }
                    },
                    "required": ["building_data"]
                }
            },
            "create_ward_simple": {
                "name": "create_ward_simple",
                "description": "Create a new ward using simple endpoint",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "ward_data": {
                            "type": "object",
                            "description": "Ward data including name, code, description, and building ID"
                        }
                    },
                    "required": ["ward_data"]
                }
            },
            "create_room_simple": {
                "name": "create_room_simple",
                "description": "Create a new room using simple endpoint",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "room_data": {
                            "type": "object",
                            "description": "Room data including name, code, description, and ward ID"
                        }
                    },
                    "required": ["room_data"]
                }
            },
            "search_rooms": {
                "name": "search_rooms",
                "description": "Search for rooms by name, code, type, or facilities",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "search_term": {
                            "type": "string",
                            "description": "Search term for room name, code, type, or facilities"
                        }
                    },
                    "required": ["search_term"]
                }
            },
            "get_isolation_rooms": {
                "name": "get_isolation_rooms",
                "description": "Get all isolation rooms in the system",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            "get_occupancy_stats": {
                "name": "get_occupancy_stats",
                "description": "Get current occupancy statistics for the IPU",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            "update_building_by_name": {
                "name": "update_building_by_name",
                "description": "Update building information by name",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "building_name": {
                            "type": "string",
                            "description": "Current name of the building to update"
                        },
                        "new_name": {
                            "type": "string",
                            "description": "New building name"
                        },
                        "code": {
                            "type": "string",
                            "description": "New building code"
                        },
                        "description": {
                            "type": "string",
                            "description": "New building description"
                        },
                        "facilities": {
                            "type": "string",
                            "description": "New facilities information"
                        }
                    },
                    "required": ["building_name"]
                }
            },
            "update_room_by_name": {
                "name": "update_room_by_name",
                "description": "Update room information by name",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "room_name": {
                            "type": "string",
                            "description": "Current name of the room to update"
                        },
                        "new_name": {
                            "type": "string",
                            "description": "New room name"
                        },
                        "code": {
                            "type": "string",
                            "description": "New room code"
                        },
                        "description": {
                            "type": "string",
                            "description": "New room description"
                        },
                        "facilities": {
                            "type": "string",
                            "description": "New facilities information"
                        },
                        "is_occupied": {
                            "type": "boolean",
                            "description": "Room occupancy status"
                        }
                    },
                    "required": ["room_name"]
                }
            },
            "update_ward_by_name": {
                "name": "update_ward_by_name",
                "description": "Update ward information by name",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "ward_name": {
                            "type": "string",
                            "description": "Current name of the ward to update"
                        },
                        "new_name": {
                            "type": "string",
                            "description": "New ward name"
                        },
                        "code": {
                            "type": "string",
                            "description": "New ward code"
                        },
                        "description": {
                            "type": "string",
                            "description": "New ward description"
                        },
                        "facilities": {
                            "type": "string",
                            "description": "New facilities information"
                        }
                    },
                    "required": ["ward_name"]
                }
            }
        }
    
    async def execute_function(self, function_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute an IPU function"""
        try:
            if function_name == "get_buildings":
                result = await self.ipu_client.get_buildings()
            elif function_name == "search_buildings":
                result = await self.ipu_client.search_buildings(arguments["search_term"])
            elif function_name == "get_wards":
                result = await self.ipu_client.get_wards()
            elif function_name == "get_wards_by_building":
                result = await self.ipu_client.get_wards_by_building(arguments["building_id"])
            elif function_name == "get_rooms":
                result = await self.ipu_client.get_rooms()
            elif function_name == "get_available_rooms":
                result = await self.ipu_client.get_available_rooms()
            elif function_name == "get_wards_by_building_name":
                result = await self.ipu_client.get_wards_by_building_name(arguments["building_name"])
            elif function_name == "get_rooms_by_ward_name":
                result = await self.ipu_client.get_rooms_by_ward_name(arguments["ward_name"])
            elif function_name == "delete_ward_by_name":
                result = await self.ipu_client.delete_ward_by_name(arguments["ward_name"])
            elif function_name == "delete_room_by_name":
                result = await self.ipu_client.delete_room_by_name(arguments["room_name"])
            elif function_name == "create_building_simple":
                result = await self.ipu_client.create_building_simple(arguments["building_data"])
            elif function_name == "create_ward_simple":
                result = await self.ipu_client.create_ward_simple(arguments["ward_data"])
            elif function_name == "create_room_simple":
                result = await self.ipu_client.create_room_simple(arguments["room_data"])
            elif function_name == "search_rooms":
                result = await self.ipu_client.search_rooms(arguments["search_term"])
            elif function_name == "get_isolation_rooms":
                result = await self.ipu_client.get_isolation_rooms()
            elif function_name == "get_occupancy_stats":
                result = await self.ipu_client.get_occupancy_stats()
            elif function_name == "update_building_by_name":
                building_name = arguments["building_name"]
                update_data = {
                    "isActive": True  # Required field according to UpdateLocalityDto schema
                }
                if arguments.get("new_name"):
                    update_data["localityTitle"] = arguments["new_name"]
                    # Generate a unique code based on the new name if no code provided
                    if not arguments.get("code"):
                        import re
                        # Create code from name: remove spaces, special chars, take first 8 chars, make uppercase
                        clean_name = re.sub(r'[^a-zA-Z0-9]', '', arguments["new_name"])
                        update_data["localityCode"] = clean_name[:8].upper()
                if arguments.get("code"):
                    update_data["localityCode"] = arguments["code"]
                if arguments.get("description"):
                    update_data["localityDescription"] = arguments["description"]
                if arguments.get("facilities"):
                    update_data["facilities"] = arguments["facilities"]
                result = await self.ipu_client.update_building_by_name(building_name, update_data)
            elif function_name == "update_room_by_name":
                room_name = arguments["room_name"]
                update_data = {
                    "isActive": True  # Required field according to UpdateLocalityDto schema
                }
                if arguments.get("new_name"):
                    update_data["localityTitle"] = arguments["new_name"]
                    # Generate a unique code based on the new name if no code provided
                    if not arguments.get("code"):
                        import re
                        import time
                        # Create code from name: remove spaces, special chars, add timestamp for uniqueness
                        clean_name = re.sub(r'[^a-zA-Z0-9]', '', arguments["new_name"])
                        timestamp = str(int(time.time()))[-4:]  # Last 4 digits of timestamp
                        update_data["localityCode"] = f"{clean_name[:4].upper()}{timestamp}"
                if arguments.get("code"):
                    update_data["localityCode"] = arguments["code"]
                if arguments.get("description"):
                    update_data["localityDescription"] = arguments["description"]
                if arguments.get("facilities"):
                    update_data["facilities"] = arguments["facilities"]
                if "is_occupied" in arguments:
                    update_data["isOccupied"] = arguments["is_occupied"]
                result = await self.ipu_client.update_room_by_name(room_name, update_data)
            elif function_name == "update_ward_by_name":
                ward_name = arguments["ward_name"]
                update_data = {
                    "isActive": True  # Required field according to UpdateLocalityDto schema
                }
                if arguments.get("new_name"):
                    update_data["localityTitle"] = arguments["new_name"]
                    # Generate a unique code based on the new name if no code provided
                    if not arguments.get("code"):
                        import re
                        import time
                        # Create code from name: remove spaces, special chars, add timestamp for uniqueness
                        clean_name = re.sub(r'[^a-zA-Z0-9]', '', arguments["new_name"])
                        timestamp = str(int(time.time()))[-3:]  # Last 3 digits of timestamp
                        update_data["localityCode"] = f"{clean_name[:5].upper()}{timestamp}"
                if arguments.get("code"):
                    update_data["localityCode"] = arguments["code"]
                if arguments.get("description"):
                    update_data["localityDescription"] = arguments["description"]
                if arguments.get("facilities"):
                    update_data["facilities"] = arguments["facilities"]
                result = await self.ipu_client.update_ward_by_name(ward_name, update_data)
            else:
                raise ValueError(f"Unknown function: {function_name}")
            
            return {"success": True, "data": result}
            
        except Exception as e:
            logger.error(f"Function execution failed for {function_name}: {e}")
            return {"success": False, "error": str(e)}


def format_data_as_table(data: List[Dict], headers: List[str], title: str = "") -> str:
    """Format data as a beautiful HTML table for better presentation"""
    if not data:
        return "No data available."

    # Extract table rows
    table_data = []
    for item in data:
        row = []
        for header in headers:
            # Handle nested keys (e.g., 'localityTitle' -> 'Name')
            value = item.get(header, 'N/A')
            if value is None:
                value = 'N/A'
            row.append(str(value))
        table_data.append(row)

    # Create HTML table with beautiful styling
    html_table = tabulate(table_data, headers=headers, tablefmt='html', stralign='left')

    # Add beautiful styling
    styled_table = html_table.replace(
        '<table>',
        '''<table style="
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
            font-family: 'Inter', Arial, sans-serif;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        ">'''
    ).replace(
        '<th>',
        '''<th style="
            border: none;
            padding: 12px 16px;
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        ">'''
    ).replace(
        '<td>',
        '''<td style="
            border: none;
            border-bottom: 1px solid #e2e8f0;
            padding: 12px 16px;
            background-color: #ffffff;
            color: #334155;
            font-size: 14px;
            line-height: 1.5;
        ">'''
    ).replace(
        '<tbody>',
        '<tbody style="background-color: #ffffff;">'
    )

    # Add hover effect with CSS
    styled_table = f'''
    <style>
        .data-table tbody tr:hover {{
            background-color: #f8fafc !important;
            transform: translateY(-1px);
            transition: all 0.2s ease;
        }}
        .data-table tbody tr:nth-child(even) {{
            background-color: #f9fafb;
        }}
    </style>
    <div class="data-table">
        {styled_table}
    </div>
    '''

    result = ""
    if title:
        result += f"{title}\n\n"
    result += styled_table

    return result


class ProfessionalLLMChatbot:
    """Professional LLM-powered chatbot for IPU management"""
    
    def __init__(self, provider: LLMProvider = LLMProvider.OPENAI, ipu_client=None):
        self.provider = provider
        self.ipu_client = ipu_client
        self.function_registry = IPUFunctionRegistry(ipu_client) if ipu_client else None
        self.conversations: Dict[str, ConversationContext] = {}
        
        # Initialize LLM client
        self._init_llm_client()
        
        # System prompt for healthcare context
        self.system_prompt = self._create_system_prompt()
    
    def _init_llm_client(self):
        """Initialize the appropriate LLM client"""
        if self.provider == LLMProvider.OPENAI and OPENAI_AVAILABLE:
            api_key = os.getenv("OPENAI_API_KEY")
            if api_key:
                self.client = openai.AsyncOpenAI(api_key=api_key)
            else:
                logger.warning("OpenAI API key not found, falling back to mock")
                self.provider = LLMProvider.MOCK

        elif self.provider == LLMProvider.ANTHROPIC and ANTHROPIC_AVAILABLE:
            api_key = os.getenv("ANTHROPIC_API_KEY")
            if api_key:
                self.client = anthropic.AsyncAnthropic(api_key=api_key)
            else:
                logger.warning("Anthropic API key not found, falling back to mock")
                self.provider = LLMProvider.MOCK

        elif self.provider == LLMProvider.HUGGINGFACE and HUGGINGFACE_AVAILABLE:
            try:
                # Initialize Hugging Face pipeline
                model_name = os.getenv("HUGGINGFACE_MODEL", "microsoft/DialoGPT-medium")
                self.client = pipeline(
                    "text-generation",
                    model=model_name,
                    tokenizer=model_name,
                    device=0 if torch.cuda.is_available() else -1,
                    max_length=1000,
                    do_sample=True,
                    temperature=0.7,
                    pad_token_id=50256
                )
                logger.info(f"Initialized Hugging Face model: {model_name}")
            except Exception as e:
                logger.warning(f"Failed to initialize Hugging Face model: {e}, falling back to mock")
                self.provider = LLMProvider.MOCK
        else:
            self.provider = LLMProvider.MOCK

        if self.provider == LLMProvider.MOCK:
            self.client = None
            logger.info("Using mock LLM for demonstration")
    
    def _create_system_prompt(self) -> str:
        """Create the system prompt for healthcare context"""
        return """You are Indici Assistant, a professional AI assistant for Inpatient Unit (IPU) management in healthcare facilities. 

Your role is to help healthcare professionals manage:
- Hospital buildings, wards, and rooms
- Bed availability and occupancy
- Patient placement and room assignments
- Facility searches and analytics

Communication Guidelines:
- Be professional, clear, and concise
- Use healthcare terminology appropriately
- Prioritize patient safety and operational efficiency
- Provide actionable information
- Ask clarifying questions when needed
- Format responses clearly with bullet points and sections

When users ask about IPU operations, use the available functions to get real-time data from the system. Always explain what you're doing and provide context for the information you retrieve.

Available Functions:
- Building management (list, search, details)
- Ward management (list by building, search)
- Room management (availability, search, isolation rooms)
- Occupancy statistics and analytics

Remember: You're assisting healthcare professionals in critical operations. Accuracy and clarity are paramount."""
    
    def get_or_create_conversation(self, session_id: str) -> ConversationContext:
        """Get existing conversation or create new one"""
        if session_id not in self.conversations:
            self.conversations[session_id] = ConversationContext(
                session_id=session_id,
                messages=[Message(
                    role="system",
                    content=self.system_prompt,
                    timestamp=datetime.now()
                )],
                user_preferences={}
            )
        
        # Update last activity
        self.conversations[session_id].last_activity = datetime.now()
        return self.conversations[session_id]

    async def process_message(self, session_id: str, user_message: str) -> Dict[str, Any]:
        """Process a user message and return AI response"""
        try:
            conversation = self.get_or_create_conversation(session_id)

            # Add user message to conversation
            user_msg = Message(
                role="user",
                content=user_message,
                timestamp=datetime.now()
            )
            conversation.messages.append(user_msg)

            # Generate AI response
            if self.provider == LLMProvider.MOCK:
                response = await self._mock_response(user_message, conversation)
            elif self.provider == LLMProvider.OPENAI:
                response = await self._openai_response(conversation)
            elif self.provider == LLMProvider.ANTHROPIC:
                response = await self._anthropic_response(conversation)
            elif self.provider == LLMProvider.HUGGINGFACE:
                response = await self._huggingface_response(conversation)
            else:
                response = {"error": "No LLM provider available"}

            # Add AI response to conversation
            if "content" in response:
                ai_msg = Message(
                    role="assistant",
                    content=response["content"],
                    timestamp=datetime.now(),
                    function_call=response.get("function_call"),
                    function_response=response.get("function_response")
                )
                conversation.messages.append(ai_msg)

            return response

        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return {
                "error": f"I apologize, but I encountered an error processing your request: {str(e)}",
                "type": "error"
            }

    def _generic_error_response(self) -> Dict[str, Any]:
        """Generic error response for unavailable functions"""
        return {
            "content": "❌ This function is not available yet. Please try another query such as:\n\n• 'Show me all buildings'\n• 'Show me available rooms'\n• 'What is the occupancy rate?'\n• 'Help me with IPU management'",
            "type": "error"
        }

    async def _handle_building_update(self, user_message: str) -> Dict[str, Any]:
        """Handle building update requests"""
        # Extract building name and new name from the message
        # Example: "update building name test333 to Buildingone"
        user_lower = user_message.lower()

        # Try to parse the update request
        if " to " in user_message:
            parts = user_message.split(" to ")
            if len(parts) == 2:
                old_name_part = parts[0]
                new_name = parts[1].strip()

                # Extract old building name - improved parsing for multi-word names
                # Handle patterns like "update building test333 to" or "update building name test333 to"
                old_name = None
                words = old_name_part.split()

                # Look for the pattern "building name" and extract everything after it
                building_name_found = False
                for i in range(len(words) - 1):
                    if words[i].lower() == "building" and words[i + 1].lower() == "name":
                        # Found "building name", extract everything after it
                        if i + 2 < len(words):
                            name_words = words[i + 2:]
                            old_name = " ".join(name_words)
                            building_name_found = True
                            break

                # If "building name" pattern not found, use fallback logic
                if not building_name_found:
                    # Find the building name - collect all words after the last command word
                    command_words = ["update", "building", "change", "modify", "edit", "rename"]
                    name_words = []

                    # Find the last command word position
                    last_command_pos = -1
                    for i, word in enumerate(words):
                        if word.lower() in command_words:
                            last_command_pos = i

                    # Collect all words after the last command word
                    if last_command_pos >= 0 and last_command_pos + 1 < len(words):
                        name_words = words[last_command_pos + 1:]
                        old_name = " ".join(name_words)
                    elif len(words) > 0:
                        # Fallback: use the last word
                        old_name = words[-1]

                if old_name and new_name and self.function_registry:
                    # Use the new update-by-name API endpoint
                    try:
                        update_result = await self.function_registry.execute_function("update_building_by_name", {
                            "building_name": old_name,
                            "new_name": new_name
                        })

                        # Even if the API reports failure, verify if the update actually worked
                        verification_result = await self.function_registry.execute_function("get_buildings", {})

                        if verification_result["success"]:
                            buildings = verification_result["data"]
                            # Check if a building with the new name exists
                            updated_building = None
                            for building in buildings:
                                if building.get('localityTitle') == new_name:
                                    updated_building = building
                                    break

                            if updated_building:
                                # Update actually worked despite API error
                                # Get all buildings to show updated list
                                all_buildings_result = await self.function_registry.execute_function("get_buildings", {})

                                success_content = f"""✅ **Building Updated Successfully**

**Previous Name:** {old_name}
**New Name:** {new_name}
**Code:** {updated_building.get('localityCode', 'N/A')}

The building has been successfully updated in the IPU system."""

                                # Add updated buildings table
                                if all_buildings_result["success"]:
                                    buildings = all_buildings_result["data"]

                                    # Prepare data for table
                                    table_data = []
                                    for building in buildings:
                                        description = building.get('localityDescription', 'N/A')
                                        if description == 'N/A' or not description:
                                            description = '—'

                                        status = "🔴 Occupied" if building.get('isOccupied', False) else "🟢 Available"

                                        table_data.append({
                                            'Building Name': building.get('localityTitle', 'Unknown'),
                                            'Code': building.get('localityCode', 'N/A'),
                                            'Description': description,
                                            'Status': status
                                        })

                                    # Create beautiful table
                                    table_html = format_data_as_table(
                                        table_data,
                                        ['Building Name', 'Code', 'Description', 'Status'],
                                        ""
                                    )

                                    success_content += f"\n\n**Updated Building List:**\n\n{table_html}"

                                return {
                                    "content": success_content,
                                    "type": "success"
                                }

                        # If verification shows the update didn't work, report the original error
                        if update_result["success"]:
                            # Get all buildings to show updated list
                            all_buildings_result = await self.function_registry.execute_function("get_buildings", {})

                            success_content = f"""✅ **Building Updated Successfully**

**Previous Name:** {old_name}
**New Name:** {new_name}

The building has been successfully updated in the IPU system."""

                            # Add updated buildings table
                            if all_buildings_result["success"]:
                                buildings = all_buildings_result["data"]

                                # Prepare data for table
                                table_data = []
                                for building in buildings:
                                    description = building.get('localityDescription', 'N/A')
                                    if description == 'N/A' or not description:
                                        description = '—'

                                    status = "🔴 Occupied" if building.get('isOccupied', False) else "🟢 Available"

                                    table_data.append({
                                        'Building Name': building.get('localityTitle', 'Unknown'),
                                        'Code': building.get('localityCode', 'N/A'),
                                        'Description': description,
                                        'Status': status
                                    })

                                # Create beautiful table
                                table_html = format_data_as_table(
                                    table_data,
                                    ['Building Name', 'Code', 'Description', 'Status'],
                                    ""
                                )

                                success_content += f"\n\n**Updated Building List:**\n\n{table_html}"

                            return {
                                "content": success_content,
                                "type": "success"
                            }
                        else:
                            return {
                                "content": f"""❌ **Update Failed**

Could not update building "{old_name}" to "{new_name}".

Error: {update_result.get('error', 'Building not found or update failed')}""",
                                "type": "error"
                            }
                    except Exception as e:
                        return {
                            "content": f"❌ Error processing update request: {str(e)}",
                            "type": "error"
                        }

        return {
            "content": """❌ **Invalid update format.**

Please use the format: "update building name [old_name] to [new_name]"

**Example:** "update building name test333 to Buildingone"

**Note:** Make sure the building name exists in the system.""",
            "type": "error"
        }

    async def _handle_ward_update(self, user_message: str) -> Dict[str, Any]:
        """Handle ward update requests"""
        # Extract ward name and new name from the message
        # Example: "update ward name WardName to NewWardName"
        user_lower = user_message.lower()

        # Try to parse the update request
        if " to " in user_message:
            parts = user_message.split(" to ")
            if len(parts) == 2:
                old_name_part = parts[0]
                new_name = parts[1].strip()

                # Extract old ward name - improved parsing for multi-word names
                old_name = None
                words = old_name_part.split()

                # Look for the pattern "ward name" and extract everything after it
                ward_name_found = False
                for i in range(len(words) - 1):
                    if words[i].lower() == "ward" and words[i + 1].lower() == "name":
                        # Found "ward name", extract everything after it
                        if i + 2 < len(words):
                            name_words = words[i + 2:]
                            old_name = " ".join(name_words)
                            ward_name_found = True
                            break

                # If "ward name" pattern not found, use fallback logic
                if not ward_name_found:
                    # Find the ward name - collect all words after the last command word
                    command_words = ["update", "ward", "change", "modify", "edit", "rename"]
                    name_words = []

                    # Find the last command word position
                    last_command_pos = -1
                    for i, word in enumerate(words):
                        if word.lower() in command_words:
                            last_command_pos = i

                    # Collect all words after the last command word
                    if last_command_pos >= 0 and last_command_pos + 1 < len(words):
                        name_words = words[last_command_pos + 1:]
                        old_name = " ".join(name_words)
                    elif len(words) > 0:
                        # Fallback: use the last word
                        old_name = words[-1]

                if old_name and new_name and self.function_registry:
                    # Use the new update-by-name API endpoint
                    try:
                        update_result = await self.function_registry.execute_function("update_ward_by_name", {
                            "ward_name": old_name,
                            "new_name": new_name
                        })

                        # Even if the API reports failure, verify if the update actually worked
                        verification_result = await self.function_registry.execute_function("get_wards", {})

                        if verification_result["success"]:
                            wards = verification_result["data"]
                            # Check if a ward with the new name exists
                            updated_ward = None
                            for ward in wards:
                                if ward.get('localityTitle') == new_name:
                                    updated_ward = ward
                                    break

                            if updated_ward:
                                # Update actually worked despite API error
                                # Get all wards to show updated list
                                all_wards_result = await self.function_registry.execute_function("get_wards", {})

                                success_content = f"""✅ **Ward Updated Successfully**

**Previous Name:** {old_name}
**New Name:** {new_name}
**Code:** {updated_ward.get('localityCode', 'N/A')}
**Building:** {updated_ward.get('parentLocalityTitle', 'N/A')}

The ward has been successfully updated in the IPU system."""

                                # Add updated wards table
                                if all_wards_result["success"]:
                                    wards = all_wards_result["data"]

                                    # Prepare data for table
                                    table_data = []
                                    for ward in wards:
                                        description = ward.get('localityDescription', 'N/A')
                                        if description == 'N/A' or not description:
                                            description = '—'

                                        building = ward.get('parentLocalityTitle', 'N/A')
                                        if building == 'N/A' or not building:
                                            building = '—'

                                        status = "🔴 Occupied" if ward.get('isOccupied', False) else "🟢 Available"

                                        table_data.append({
                                            'Ward Name': ward.get('localityTitle', 'Unknown'),
                                            'Code': ward.get('localityCode', 'N/A'),
                                            'Building': building,
                                            'Description': description,
                                            'Status': status
                                        })

                                    # Create beautiful table
                                    table_html = format_data_as_table(
                                        table_data,
                                        ['Ward Name', 'Code', 'Building', 'Description', 'Status'],
                                        ""
                                    )

                                    success_content += f"\n\n**Updated Ward List:**\n\n{table_html}"

                                return {
                                    "content": success_content,
                                    "type": "success"
                                }

                        # If verification shows the update didn't work, report the original error
                        if update_result["success"]:
                            # Get all wards to show updated list
                            all_wards_result = await self.function_registry.execute_function("get_wards", {})

                            success_content = f"""✅ **Ward Updated Successfully**

**Previous Name:** {old_name}
**New Name:** {new_name}

The ward has been successfully updated in the IPU system."""

                            # Add updated wards table
                            if all_wards_result["success"]:
                                wards = all_wards_result["data"]

                                # Prepare data for table
                                table_data = []
                                for ward in wards:
                                    description = ward.get('localityDescription', 'N/A')
                                    if description == 'N/A' or not description:
                                        description = '—'

                                    building = ward.get('parentLocalityTitle', 'N/A')
                                    if building == 'N/A' or not building:
                                        building = '—'

                                    status = "🔴 Occupied" if ward.get('isOccupied', False) else "🟢 Available"

                                    table_data.append({
                                        'Ward Name': ward.get('localityTitle', 'Unknown'),
                                        'Code': ward.get('localityCode', 'N/A'),
                                        'Building': building,
                                        'Description': description,
                                        'Status': status
                                    })

                                # Create beautiful table
                                table_html = format_data_as_table(
                                    table_data,
                                    ['Ward Name', 'Code', 'Building', 'Description', 'Status'],
                                    ""
                                )

                                success_content += f"\n\n**Updated Ward List:**\n\n{table_html}"

                            return {
                                "content": success_content,
                                "type": "success"
                            }
                        else:
                            return {
                                "content": f"""❌ **Update Failed**

Could not update ward "{old_name}" to "{new_name}".

Error: {update_result.get('error', 'Ward not found or update failed')}""",
                                "type": "error"
                            }
                    except Exception as e:
                        return {
                            "content": f"❌ Error processing update request: {str(e)}",
                            "type": "error"
                        }

        return {
            "content": """❌ **Invalid update format.**

Please use the format: **"update ward name [current_name] to [new_name]"**

**Examples:**
• "update ward name Surgical Ward to Operating Ward"
• "change ward Emergency to Emergency Department"

**Note:** Make sure the ward name exists in the system.""",
            "type": "error"
        }

    async def _handle_room_update(self, user_message: str) -> Dict[str, Any]:
        """Handle room update requests"""
        # Extract room name and new name from the message
        # Example: "update room name Room1 to NewRoom"
        user_lower = user_message.lower()

        # Try to parse the update request
        if " to " in user_message:
            parts = user_message.split(" to ")
            if len(parts) == 2:
                old_name_part = parts[0]
                new_name = parts[1].strip()

                # Extract old room name - improved parsing for multi-word names
                old_name = None
                words = old_name_part.split()

                # Look for the pattern "room name" and extract everything after it
                room_name_found = False
                for i in range(len(words) - 1):
                    if words[i].lower() == "room" and words[i + 1].lower() == "name":
                        # Found "room name", extract everything after it
                        if i + 2 < len(words):
                            name_words = words[i + 2:]
                            old_name = " ".join(name_words)
                            room_name_found = True
                            break

                # If "room name" pattern not found, use fallback logic
                if not room_name_found:
                    # Find the room name - collect all words after the last command word
                    command_words = ["update", "room", "change", "modify", "edit", "rename"]
                    name_words = []

                    # Find the last command word position
                    last_command_pos = -1
                    for i, word in enumerate(words):
                        if word.lower() in command_words:
                            last_command_pos = i

                    # Collect all words after the last command word
                    if last_command_pos >= 0 and last_command_pos + 1 < len(words):
                        name_words = words[last_command_pos + 1:]
                        old_name = " ".join(name_words)
                    elif len(words) > 0:
                        # Fallback: use the last word
                        old_name = words[-1]

                if old_name and new_name and self.function_registry:
                    # Use the new update-by-name API endpoint
                    try:
                        update_result = await self.function_registry.execute_function("update_room_by_name", {
                            "room_name": old_name,
                            "new_name": new_name
                        })

                        # Even if the API reports failure, verify if the update actually worked
                        # This is a workaround for the API returning error codes despite successful updates
                        verification_result = await self.function_registry.execute_function("get_rooms", {})

                        if verification_result["success"]:
                            rooms = verification_result["data"]
                            # Check if a room with the new name exists
                            updated_room = None
                            for room in rooms:
                                if room.get('localityTitle') == new_name:
                                    updated_room = room
                                    break

                            if updated_room:
                                # Update actually worked despite API error
                                # Get all rooms to show updated list
                                all_rooms_result = await self.function_registry.execute_function("get_rooms", {})

                                success_content = f"""✅ **Room Updated Successfully**

**Previous Name:** {old_name}
**New Name:** {new_name}
**Code:** {updated_room.get('localityCode', 'N/A')}
**Ward:** {updated_room.get('parentLocalityTitle', 'N/A')}

The room has been successfully updated in the IPU system."""

                                # Add updated rooms table
                                if all_rooms_result["success"]:
                                    rooms = all_rooms_result["data"]

                                    # Prepare data for table
                                    table_data = []
                                    for room in rooms:
                                        status = "🔴 Occupied" if room.get('isOccupied', False) else "🟢 Available"
                                        ward = room.get('parentLocalityTitle', 'N/A')
                                        if ward == 'N/A' or not ward:
                                            ward = '—'

                                        table_data.append({
                                            'Room Name': room.get('localityTitle', 'Unknown'),
                                            'Code': room.get('localityCode', 'N/A'),
                                            'Ward': ward,
                                            'Status': status
                                        })

                                    # Create beautiful table
                                    table_html = format_data_as_table(
                                        table_data,
                                        ['Room Name', 'Code', 'Ward', 'Status'],
                                        ""
                                    )

                                    success_content += f"\n\n**Updated Room List:**\n\n{table_html}"

                                return {
                                    "content": success_content,
                                    "type": "success"
                                }

                        # If verification shows the update didn't work, report the original error
                        if update_result["success"]:
                            # Get all rooms to show updated list
                            all_rooms_result = await self.function_registry.execute_function("get_rooms", {})

                            success_content = f"""✅ **Room Updated Successfully**

**Previous Name:** {old_name}
**New Name:** {new_name}

The room has been successfully updated in the IPU system."""

                            # Add updated rooms table
                            if all_rooms_result["success"]:
                                rooms = all_rooms_result["data"]

                                # Prepare data for table
                                table_data = []
                                for room in rooms:
                                    status = "🔴 Occupied" if room.get('isOccupied', False) else "🟢 Available"
                                    ward = room.get('parentLocalityTitle', 'N/A')
                                    if ward == 'N/A' or not ward:
                                        ward = '—'

                                    table_data.append({
                                        'Room Name': room.get('localityTitle', 'Unknown'),
                                        'Code': room.get('localityCode', 'N/A'),
                                        'Ward': ward,
                                        'Status': status
                                    })

                                # Create beautiful table
                                table_html = format_data_as_table(
                                    table_data,
                                    ['Room Name', 'Code', 'Ward', 'Status'],
                                    ""
                                )

                                success_content += f"\n\n**Updated Room List:**\n\n{table_html}"

                            return {
                                "content": success_content,
                                "type": "success"
                            }
                        else:
                            return {
                                "content": f"""❌ **Update Failed**

Could not update room "{old_name}" to "{new_name}".

Error: {update_result.get('error', 'Room not found or update failed')}""",
                                "type": "error"
                            }
                    except Exception as e:
                        return {
                            "content": f"❌ Error processing update request: {str(e)}",
                            "type": "error"
                        }

        return {
            "content": """❌ **Invalid update format.**

Please use the format: "update room name [old_name] to [new_name]"

**Example:** "update room name Room1 to NewRoom"

**Note:** Make sure the room name exists in the system.""",
            "type": "error"
        }

    async def _mock_response(self, user_message: str, conversation: ConversationContext) -> Dict[str, Any]:
        """Generate mock response for demonstration"""
        user_lower = user_message.lower()

        # Handle delete queries first
        if any(keyword in user_lower for keyword in ["delete", "remove", "drop"]):
            # Ward delete
            if "ward" in user_lower:
                # Extract ward name from patterns like:
                # "delete ward wardname"
                # "remove ward wardname"
                import re
                patterns = [
                    r'(?:delete|remove|drop)\s+ward\s+([^\s]+)',
                    r'(?:delete|remove|drop)\s+([^\s]+)\s+ward',
                    r'ward\s+([^\s]+)\s+(?:delete|remove|drop)'
                ]

                ward_name = None

                for pattern in patterns:
                    match = re.search(pattern, user_message, re.IGNORECASE)
                    if match:
                        ward_name = match.group(1)
                        break

                if ward_name and self.function_registry:
                    try:
                        delete_result = await self.function_registry.execute_function("delete_ward_by_name", {
                            "ward_name": ward_name
                        })

                        if delete_result.get("success"):
                            return {
                                "content": f"""✅ **Ward Deleted Successfully**

**Deleted Ward:** {ward_name}

The ward has been successfully removed from the IPU system.""",
                                "type": "success"
                            }
                        else:
                            return {
                                "content": f"""❌ **Delete Failed**

Could not delete ward "{ward_name}".

Error: {delete_result.get('message', 'Ward not found or delete failed')}""",
                                "type": "error"
                            }
                    except Exception as e:
                        return {
                            "content": f"❌ Error processing delete request: {str(e)}",
                            "type": "error"
                        }

            # Room delete
            elif "room" in user_lower:
                # Extract room name from patterns like:
                # "delete room roomname"
                # "remove room roomname"
                import re
                patterns = [
                    r'(?:delete|remove|drop)\s+room\s+([^\s]+)',
                    r'(?:delete|remove|drop)\s+([^\s]+)\s+room',
                    r'room\s+([^\s]+)\s+(?:delete|remove|drop)'
                ]

                room_name = None

                for pattern in patterns:
                    match = re.search(pattern, user_message, re.IGNORECASE)
                    if match:
                        room_name = match.group(1)
                        break

                if room_name and self.function_registry:
                    try:
                        delete_result = await self.function_registry.execute_function("delete_room_by_name", {
                            "room_name": room_name
                        })

                        if delete_result.get("success"):
                            return {
                                "content": f"""✅ **Room Deleted Successfully**

**Deleted Room:** {room_name}

The room has been successfully removed from the IPU system.""",
                                "type": "success"
                            }
                        else:
                            return {
                                "content": f"""❌ **Delete Failed**

Could not delete room "{room_name}".

Error: {delete_result.get('message', 'Room not found or delete failed')}""",
                                "type": "error"
                            }
                    except Exception as e:
                        return {
                            "content": f"❌ Error processing delete request: {str(e)}",
                            "type": "error"
                        }

        # Handle create queries
        elif any(keyword in user_lower for keyword in ["create", "add", "new"]):
            # Building create
            if "building" in user_lower:
                # Extract building details from patterns like:
                # "create building BuildingName"
                # "add new building BuildingName with code BC123"
                import re
                patterns = [
                    r'(?:create|add|new)\s+(?:new\s+)?building\s+([^\s]+)(?:\s+with\s+code\s+([^\s]+))?',
                    r'building\s+([^\s]+)(?:\s+code\s+([^\s]+))?'
                ]

                building_name = None
                building_code = None

                for pattern in patterns:
                    match = re.search(pattern, user_message, re.IGNORECASE)
                    if match:
                        building_name = match.group(1)
                        building_code = match.group(2) if match.lastindex >= 2 else None
                        break

                if building_name and self.function_registry:
                    try:
                        import time
                        building_data = {
                            "localityTitle": building_name,
                            "localityCode": building_code or f"BLD{int(time.time()) % 10000}",
                            "localityDescription": f"Building {building_name}",
                            "isActive": True
                        }

                        create_result = await self.function_registry.execute_function("create_building_simple", {
                            "building_data": building_data
                        })

                        if create_result.get("success"):
                            return {
                                "content": f"""✅ **Building Created Successfully**

**Building Name:** {building_name}
**Code:** {building_data['localityCode']}
**Description:** {building_data['localityDescription']}

The building has been successfully added to the IPU system.""",
                                "type": "success"
                            }
                        else:
                            return {
                                "content": f"""❌ **Create Failed**

Could not create building "{building_name}".

Error: {create_result.get('message', 'Building creation failed')}""",
                                "type": "error"
                            }
                    except Exception as e:
                        return {
                            "content": f"❌ Error processing create request: {str(e)}",
                            "type": "error"
                        }

        # Handle update/edit queries (after delete and create)
        elif any(keyword in user_lower for keyword in ["update", "edit", "change", "modify", "rename"]):
            # More precise detection: look for entity type keyword near the update keyword
            words = user_lower.split()

            # Find the position of update keywords
            update_positions = []
            for i, word in enumerate(words):
                if word in ["update", "edit", "change", "modify", "rename"]:
                    update_positions.append(i)

            # Check for entity type within 3 words after any update keyword
            entity_type = None
            for pos in update_positions:
                # Check the next 3 words after the update keyword
                for i in range(pos + 1, min(pos + 4, len(words))):
                    if i < len(words):
                        if words[i] in ["building", "buildings"]:
                            entity_type = "building"
                            break
                        elif words[i] in ["ward", "wards"]:
                            entity_type = "ward"
                            break
                        elif words[i] in ["room", "rooms"]:
                            entity_type = "room"
                            break
                if entity_type:
                    break

            # Route to appropriate handler based on detected entity type
            if entity_type == "building":
                return await self._handle_building_update(user_message)
            elif entity_type == "ward":
                return await self._handle_ward_update(user_message)
            elif entity_type == "room":
                return await self._handle_room_update(user_message)
            else:
                return self._generic_error_response()

        # Simulate function calling for IPU operations
        elif any(keyword in user_lower for keyword in ["building", "buildings"]):
            if self.function_registry:
                function_result = await self.function_registry.execute_function("get_buildings", {})
                if function_result["success"]:
                    buildings = function_result["data"]

                    if buildings and len(buildings) > 0:
                        # Prepare data for table (removed ID column)
                        table_data = []
                        for building in buildings:
                            description = building.get('localityDescription', 'N/A')
                            if description == 'N/A' or not description:
                                description = '—'  # Better visual for empty descriptions

                            table_data.append({
                                'Building Name': building.get('localityTitle', 'Unknown'),
                                'Code': building.get('localityCode', 'N/A'),
                                'Description': description
                            })

                        # Create beautiful table
                        table_html = format_data_as_table(
                            table_data,
                            ['Building Name', 'Code', 'Description'],
                            ""
                        )

                        content = f"I found {len(buildings)} buildings in the IPU system:\n\n{table_html}"
                    else:
                        content = "No buildings found in the IPU system."

                    return {
                        "content": content,
                        "type": "success"
                    }
                else:
                    return self._generic_error_response()
            else:
                return self._generic_error_response()

        # Handle room queries (must come before ward queries to catch "rooms in ward" patterns)
        elif any(keyword in user_lower for keyword in ["room", "rooms", "bed", "beds"]):
            # Check if user wants rooms by ward (prioritize this over ward by building)
            room_by_ward_condition = ("ward" in user_lower and "room" in user_lower) or ("in" in user_lower and "room" in user_lower and any(word in user_lower for word in ["surgical", "emergency", "ward"]))
            logger.info(f"Room by ward condition: {room_by_ward_condition} for message: {user_message}")

            if room_by_ward_condition:
                # Extract ward name from query
                ward_name = None

                # Try to extract ward name from patterns like:
                # "rooms in surgical ward", "rooms of ward surgical", "list rooms of surgical"
                import re
                patterns = [
                    r'room.*(?:in|of|for)\s+(?:ward\s+)?([a-zA-Z0-9_\-\s]+?)(?:\s*$|\s+(?:and|or|,))',
                    r'(?:in|of|for)\s+ward\s+([a-zA-Z0-9_\-\s]+?)(?:\s*$|\s+(?:and|or|,))',
                    r'(?:in|of)\s+([a-zA-Z0-9_\-\s]+?\s+ward)(?:\s*$|\s+(?:and|or|,))',
                    r'(?:in|of)\s+([a-zA-Z0-9_\-\s]+?)(?:\s*$|\s+(?:and|or|,))'
                ]

                for pattern in patterns:
                    match = re.search(pattern, user_message, re.IGNORECASE)
                    if match:
                        ward_name = match.group(1).strip()
                        break

                if ward_name and self.function_registry:
                    try:
                        function_result = await self.function_registry.execute_function("get_rooms_by_ward_name", {
                            "ward_name": ward_name
                        })

                        if function_result["success"]:
                            rooms = function_result["data"]

                            if rooms and len(rooms) > 0:
                                # Filter for active rooms (isActive = 1)
                                active_rooms = [room for room in rooms if room.get('isActive', 1) == 1]

                                if active_rooms:
                                    # Prepare data for table
                                    table_data = []
                                    for room in active_rooms:
                                        facilities = room.get('facilities', 'N/A')
                                        if facilities == 'N/A' or not facilities:
                                            facilities = '—'

                                        status = "🔴 Occupied" if room.get('isOccupied', False) else "🟢 Available"

                                        table_data.append({
                                            'Room Name': room.get('localityTitle', 'Unknown'),
                                            'Code': room.get('localityCode', 'N/A'),
                                            'Status': status,
                                            'Facilities': facilities
                                        })

                                    # Create beautiful table
                                    table_html = format_data_as_table(
                                        table_data,
                                        ['Room Name', 'Code', 'Status', 'Facilities'],
                                        ""
                                    )

                                    content = f"I found {len(active_rooms)} rooms in ward '{ward_name}':\n\n{table_html}"
                                else:
                                    content = f"No active rooms found in ward '{ward_name}'."
                            else:
                                content = f"No rooms found in ward '{ward_name}'."

                            return {
                                "content": content,
                                "type": "success"
                            }
                        else:
                            return {
                                "content": f"❌ Could not find ward '{ward_name}' or retrieve its rooms.",
                                "type": "error"
                            }
                    except Exception as e:
                        return {
                            "content": f"❌ Error retrieving rooms for ward '{ward_name}': {str(e)}",
                            "type": "error"
                        }

            # Determine if user wants all rooms or just available rooms
            elif "available" in user_lower or "free" in user_lower or "vacant" in user_lower:
                # Available rooms query - isOccupied = 0 (not occupied)
                if self.function_registry:
                    function_result = await self.function_registry.execute_function("get_available_rooms", {})
                    if function_result["success"]:
                        rooms = function_result["data"]

                        if rooms and len(rooms) > 0:
                            # Filter for available rooms (isOccupied = 0) and active rooms (isActive = 1)
                            available_rooms = [room for room in rooms if room.get('isActive', 1) == 1 and room.get('isOccupied', 0) == 0]

                            if available_rooms:
                                # Prepare data for table
                                table_data = []
                                for room in available_rooms:
                                    facilities = room.get('facilities', 'N/A')
                                    if facilities == 'N/A' or not facilities:
                                        facilities = '—'

                                    ward = room.get('parentLocalityTitle', 'N/A')
                                    if ward == 'N/A' or not ward:
                                        ward = '—'

                                    table_data.append({
                                        'Room Name': room.get('localityTitle', 'Unknown'),
                                        'Ward': ward,
                                        'Status': '🟢 Available',
                                        'Facilities': facilities
                                    })

                                # Create beautiful table
                                table_html = format_data_as_table(
                                    table_data,
                                    ['Room Name', 'Ward', 'Status', 'Facilities'],
                                    ""
                                )

                                content = f"I found {len(available_rooms)} available rooms:\n\n{table_html}"
                            else:
                                content = "No available rooms found at this time."
                        else:
                            content = "No available rooms found at this time."

                        return {
                            "content": content,
                            "type": "success"
                        }
                    else:
                        return self._generic_error_response()
                else:
                    return self._generic_error_response()

            elif "all" in user_lower or "list" in user_lower or ("show" in user_lower and "room" in user_lower):
                # All rooms query - isActive = 1 (active rooms)
                if self.function_registry:
                    function_result = await self.function_registry.execute_function("get_rooms", {})
                    if function_result["success"]:
                        rooms = function_result["data"]

                        if rooms and len(rooms) > 0:
                            # Filter for active rooms only (isActive = 1)
                            active_rooms = [room for room in rooms if room.get('isActive', 1) == 1]

                            if active_rooms:
                                # Prepare data for table
                                table_data = []
                                for room in active_rooms:
                                    facilities = room.get('facilities', 'N/A')
                                    if facilities == 'N/A' or not facilities:
                                        facilities = '—'

                                    ward = room.get('parentLocalityTitle', 'N/A')
                                    if ward == 'N/A' or not ward:
                                        ward = '—'

                                    # Determine status based on isOccupied
                                    is_occupied = room.get('isOccupied', 0)
                                    status = '🔴 Occupied' if is_occupied == 1 else '🟢 Available'

                                    table_data.append({
                                        'Room Name': room.get('localityTitle', 'Unknown'),
                                        'Ward': ward,
                                        'Status': status,
                                        'Facilities': facilities
                                    })

                                # Create beautiful table
                                table_html = format_data_as_table(
                                    table_data,
                                    ['Room Name', 'Ward', 'Status', 'Facilities'],
                                    ""
                                )

                                content = f"I found {len(active_rooms)} active rooms:\n\n{table_html}"
                            else:
                                content = "No active rooms found in the system."
                        else:
                            content = "No rooms found in the system."

                        return {
                            "content": content,
                            "type": "success"
                        }
                    else:
                        return self._generic_error_response()
                else:
                    return self._generic_error_response()
            else:
                return self._generic_error_response()

        # Handle ward queries
        elif any(keyword in user_lower for keyword in ["ward", "wards"]):
            # Check if user wants wards by building (but not rooms by ward)
            if ("building" in user_lower or ("in" in user_lower and "ward" in user_lower and "room" not in user_lower)) and any(word in user_lower for word in ["demo", "test", "main", "surgical", "building"]):
                # Extract building name from query
                building_name = None

                # Try to extract building name from patterns like:
                # "wards in demo785", "wards of building demo785", "list wards of demo785"
                import re
                patterns = [
                    r'ward.*(?:in|of|for)\s+(?:building\s+)?([a-zA-Z0-9_\-\s]+?)(?:\s*$|\s+(?:and|or|,))',
                    r'(?:in|of|for)\s+building\s+([a-zA-Z0-9_\-\s]+?)(?:\s*$|\s+(?:and|or|,))',
                    r'building\s+([a-zA-Z0-9_\-\s]+?)(?:\s*$|\s+(?:and|or|,))',
                    r'(?:in|of)\s+([a-zA-Z0-9_\-\s]+?)(?:\s*$|\s+(?:and|or|,))'
                ]

                for pattern in patterns:
                    match = re.search(pattern, user_message, re.IGNORECASE)
                    if match:
                        building_name = match.group(1).strip()
                        break

                if building_name and self.function_registry:
                    try:
                        function_result = await self.function_registry.execute_function("get_wards_by_building_name", {
                            "building_name": building_name
                        })

                        if function_result["success"]:
                            wards = function_result["data"]

                            if wards and len(wards) > 0:
                                # Prepare data for table
                                table_data = []
                                for ward in wards:
                                    description = ward.get('localityDescription', 'N/A')
                                    if description == 'N/A' or not description:
                                        description = '—'

                                    building = ward.get('parentLocalityTitle', 'N/A')
                                    if building == 'N/A' or not building:
                                        building = '—'

                                    table_data.append({
                                        'Ward Name': ward.get('localityTitle', 'Unknown'),
                                        'Code': ward.get('localityCode', 'N/A'),
                                        'Building': building,
                                        'Description': description
                                    })

                                # Create beautiful table
                                table_html = format_data_as_table(
                                    table_data,
                                    ['Ward Name', 'Code', 'Building', 'Description'],
                                    ""
                                )

                                content = f"I found {len(wards)} wards in building '{building_name}':\n\n{table_html}"
                            else:
                                content = f"No wards found in building '{building_name}'."

                            return {
                                "content": content,
                                "type": "success"
                            }
                        else:
                            return {
                                "content": f"❌ Could not find building '{building_name}' or retrieve its wards.",
                                "type": "error"
                            }
                    except Exception as e:
                        return {
                            "content": f"❌ Error retrieving wards for building '{building_name}': {str(e)}",
                            "type": "error"
                        }

            # Default: show all wards
            if self.function_registry:
                function_result = await self.function_registry.execute_function("get_wards", {})
                if function_result["success"]:
                    wards = function_result["data"]

                    if wards and len(wards) > 0:
                        # Prepare data for table
                        table_data = []
                        for ward in wards:
                            description = ward.get('localityDescription', 'N/A')
                            if description == 'N/A' or not description:
                                description = '—'

                            building = ward.get('parentLocalityTitle', 'N/A')
                            if building == 'N/A' or not building:
                                building = '—'

                            table_data.append({
                                'Ward Name': ward.get('localityTitle', 'Unknown'),
                                'Code': ward.get('localityCode', 'N/A'),
                                'Building': building,
                                'Description': description
                            })

                        # Create beautiful table
                        table_html = format_data_as_table(
                            table_data,
                            ['Ward Name', 'Code', 'Building', 'Description'],
                            ""
                        )

                        content = f"I found {len(wards)} wards in the IPU system:\n\n{table_html}"
                    else:
                        content = "No wards found in the IPU system."

                    return {
                        "content": content,
                        "type": "success"
                    }
                else:
                    return self._generic_error_response()
            else:
                return self._generic_error_response()




        elif any(keyword in user_lower for keyword in ["occupancy", "statistics", "stats", "rate"]):
            if self.function_registry:
                function_result = await self.function_registry.execute_function("get_occupancy_stats", {})
                if function_result["success"]:
                    stats = function_result["data"]
                    occupancy_rate = stats.get("occupancyRate", 0)
                    status_emoji = "🔴" if occupancy_rate > 80 else "🟡" if occupancy_rate > 60 else "🟢"

                    # Prepare data for beautiful table
                    table_data = [
                        {'Metric': 'Occupancy Rate', 'Value': f"{occupancy_rate}%", 'Indicator': status_emoji},
                        {'Metric': 'Total Rooms', 'Value': str(stats.get('totalRooms', 0)), 'Indicator': '🏥'},
                        {'Metric': 'Available Rooms', 'Value': str(stats.get('availableRooms', 0)), 'Indicator': '🟢'},
                        {'Metric': 'Occupied Rooms', 'Value': str(stats.get('occupiedRooms', 0)), 'Indicator': '🔴'},
                        {'Metric': 'Last Updated', 'Value': datetime.now().strftime('%Y-%m-%d %H:%M'), 'Indicator': '🕒'}
                    ]

                    # Create beautiful table
                    table_html = format_data_as_table(
                        table_data,
                        ['Metric', 'Value', 'Indicator'],
                        ""
                    )

                    content = f"📊 Current IPU Occupancy Statistics:\n\n{table_html}"

                    return {
                        "content": content,
                        "type": "success"
                    }
                else:
                    return self._generic_error_response()
            else:
                return self._generic_error_response()

        # Check for other common queries that might not be available
        elif any(keyword in user_lower for keyword in ["patient", "admit", "discharge", "transfer", "report", "reports", "analytics", "schedule", "medication", "treatment", "care", "plan"]):
            return self._generic_error_response()

        # Default intelligent response
        return {
            "content": f"""Hello! I'm Indici Assistant, your professional IPU management AI.

I can help you with:

🏢 **Building Management**
• Show me all buildings
• List hospital buildings
• Get building information

🏥 **Room Operations**
• Show me available rooms
• Check room availability
• Find free beds

📊 **Analytics & Statistics**
• What is the occupancy rate?
• Show occupancy statistics
• Current bed utilization

**How can I assist you with IPU operations today?**

*Try asking: "Show me all buildings" or "What is the occupancy rate?"*""",
            "type": "info"
        }

    async def _openai_response(self, conversation: ConversationContext) -> Dict[str, Any]:
        """Generate response using OpenAI"""
        try:
            # Prepare messages for OpenAI format
            messages = []
            for msg in conversation.messages[-10:]:  # Last 10 messages for context
                messages.append({
                    "role": msg.role,
                    "content": msg.content
                })

            # Prepare function definitions
            functions = None
            if self.function_registry:
                functions = list(self.function_registry.functions.values())

            # Call OpenAI API
            response = await self.client.chat.completions.create(
                model="gpt-4-turbo-preview",
                messages=messages,
                functions=functions,
                function_call="auto" if functions else None,
                temperature=0.7,
                max_tokens=1000
            )

            message = response.choices[0].message

            # Handle function calls
            if message.function_call:
                function_name = message.function_call.name
                function_args = json.loads(message.function_call.arguments)

                # Execute function
                function_result = await self.function_registry.execute_function(function_name, function_args)

                # Generate follow-up response with function result
                follow_up_messages = messages + [
                    {
                        "role": "assistant",
                        "content": None,
                        "function_call": {
                            "name": function_name,
                            "arguments": message.function_call.arguments
                        }
                    },
                    {
                        "role": "function",
                        "name": function_name,
                        "content": json.dumps(function_result)
                    }
                ]

                follow_up_response = await self.client.chat.completions.create(
                    model="gpt-4-turbo-preview",
                    messages=follow_up_messages,
                    temperature=0.7,
                    max_tokens=1000
                )

                return {
                    "content": follow_up_response.choices[0].message.content,
                    "type": "success",
                    "function_call": {"name": function_name, "arguments": function_args},
                    "function_response": function_result
                }

            return {
                "content": message.content,
                "type": "success"
            }

        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            return await self._mock_response(conversation.messages[-1].content, conversation)

    async def _anthropic_response(self, conversation: ConversationContext) -> Dict[str, Any]:
        """Generate response using Anthropic Claude"""
        try:
            # Prepare messages for Anthropic format
            messages = []
            for msg in conversation.messages[-10:]:  # Last 10 messages for context
                if msg.role != "system":  # Anthropic handles system prompt separately
                    messages.append({
                        "role": msg.role,
                        "content": msg.content
                    })

            # Call Anthropic API
            response = await self.client.messages.create(
                model="claude-3-sonnet-20240229",
                system=self.system_prompt,
                messages=messages,
                max_tokens=1000,
                temperature=0.7
            )

            return {
                "content": response.content[0].text,
                "type": "success"
            }

        except Exception as e:
            logger.error(f"Anthropic API error: {e}")
            return await self._mock_response(conversation.messages[-1].content, conversation)

    async def _huggingface_response(self, conversation: ConversationContext) -> Dict[str, Any]:
        """Generate response using Hugging Face model"""
        try:
            # Get the last user message
            user_message = conversation.messages[-1].content

            # Prepare context from recent messages
            context = ""
            for msg in conversation.messages[-5:]:  # Last 5 messages for context
                if msg.role == "user":
                    context += f"User: {msg.content}\n"
                elif msg.role == "assistant":
                    context += f"Assistant: {msg.content}\n"

            # Add current user message
            prompt = f"{context}Assistant:"

            # Generate response using Hugging Face pipeline
            response = self.client(
                prompt,
                max_length=len(prompt.split()) + 100,
                num_return_sequences=1,
                temperature=0.7,
                do_sample=True,
                pad_token_id=50256
            )

            # Extract generated text
            generated_text = response[0]['generated_text']

            # Extract only the assistant's response
            assistant_response = generated_text.split("Assistant:")[-1].strip()

            # Clean up the response
            if assistant_response:
                # Remove any trailing user prompts
                if "User:" in assistant_response:
                    assistant_response = assistant_response.split("User:")[0].strip()

                return {
                    "content": assistant_response,
                    "type": "success"
                }
            else:
                # Fallback to mock response if generation fails
                return await self._mock_response(user_message, conversation)

        except Exception as e:
            logger.error(f"Hugging Face model error: {e}")
            return await self._mock_response(conversation.messages[-1].content, conversation)

    def get_conversation_summary(self, session_id: str) -> Dict[str, Any]:
        """Get conversation summary and statistics"""
        if session_id not in self.conversations:
            return {"error": "Conversation not found"}

        conversation = self.conversations[session_id]
        user_messages = [msg for msg in conversation.messages if msg.role == "user"]
        ai_messages = [msg for msg in conversation.messages if msg.role == "assistant"]

        return {
            "session_id": session_id,
            "message_count": len(conversation.messages),
            "user_messages": len(user_messages),
            "ai_messages": len(ai_messages),
            "last_activity": conversation.last_activity.isoformat(),
            "current_topic": conversation.current_topic,
            "duration": (datetime.now() - conversation.messages[0].timestamp).total_seconds() if conversation.messages else 0
        }

    def clear_conversation(self, session_id: str) -> bool:
        """Clear conversation history"""
        if session_id in self.conversations:
            del self.conversations[session_id]
            return True
        return False


# Factory function to create LLM chatbot
def create_llm_chatbot(provider: str = "openai", ipu_client=None) -> ProfessionalLLMChatbot:
    """Create LLM chatbot instance"""
    provider_enum = LLMProvider(provider.lower())
    return ProfessionalLLMChatbot(provider_enum, ipu_client)
