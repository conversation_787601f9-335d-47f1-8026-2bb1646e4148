#!/usr/bin/env python3
"""
Get Swagger documentation from IPU API
"""

import asyncio
import httpx
import json

async def get_swagger_docs():
    """Get and analyze Swagger documentation"""
    print("📖 Getting IPU API Documentation")
    print("=" * 50)
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get('http://localhost:5077/swagger/v1/swagger.json')
            data = response.json()
            
            print("🔍 API Information:")
            print(f"Title: {data.get('info', {}).get('title', 'N/A')}")
            print(f"Version: {data.get('info', {}).get('version', 'N/A')}")
            print(f"Description: {data.get('info', {}).get('description', 'N/A')}")
            
            print(f"\n📋 Available Endpoints:")
            print("-" * 50)
            
            paths = data.get('paths', {})
            endpoints_by_category = {}
            
            for path, methods in paths.items():
                category = path.split('/')[2] if len(path.split('/')) > 2 else 'root'
                if category not in endpoints_by_category:
                    endpoints_by_category[category] = []
                
                for method, details in methods.items():
                    summary = details.get('summary', 'No description')
                    endpoints_by_category[category].append({
                        'path': path,
                        'method': method.upper(),
                        'summary': summary,
                        'operationId': details.get('operationId', ''),
                        'parameters': details.get('parameters', []),
                        'requestBody': details.get('requestBody', {}),
                        'responses': details.get('responses', {})
                    })
            
            for category, endpoints in sorted(endpoints_by_category.items()):
                print(f"\n🏷️  {category.upper()} ENDPOINTS:")
                for endpoint in endpoints:
                    print(f"   {endpoint['method']} {endpoint['path']}")
                    print(f"      📝 {endpoint['summary']}")
                    if endpoint['operationId']:
                        print(f"      🔧 Operation: {endpoint['operationId']}")
                    
                    # Show parameters
                    if endpoint['parameters']:
                        print(f"      📥 Parameters:")
                        for param in endpoint['parameters']:
                            param_type = param.get('schema', {}).get('type', param.get('type', 'unknown'))
                            required = " (required)" if param.get('required', False) else ""
                            print(f"         - {param['name']}: {param_type}{required}")
                    
                    # Show request body
                    if endpoint['requestBody']:
                        print(f"      📤 Request Body: Required")
                        content = endpoint['requestBody'].get('content', {})
                        for content_type in content.keys():
                            print(f"         Content-Type: {content_type}")
                    
                    print()
            
            # Save full documentation for reference
            with open('swagger_docs.json', 'w') as f:
                json.dump(data, f, indent=2)
            print("💾 Full documentation saved to swagger_docs.json")
            
        except Exception as e:
            print(f"❌ Error getting Swagger docs: {e}")

if __name__ == '__main__':
    asyncio.run(get_swagger_docs())
