<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Indici Assistant - Healthcare Management</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            height: 100vh;
            display: flex;
            color: #334155;
            overflow: hidden;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 320px;
            background: #ffffff;
            border-right: 1px solid #e2e8f0;
            display: flex;
            flex-direction: column;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 10;
        }

        .sidebar-header {
            padding: 24px;
            border-bottom: 1px solid #e2e8f0;
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
        }

        .sidebar-header h1 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .sidebar-header p {
            font-size: 0.875rem;
            opacity: 0.9;
            margin-bottom: 12px;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.75rem;
            background: rgba(255, 255, 255, 0.1);
            padding: 6px 12px;
            border-radius: 6px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .sidebar-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        .section {
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: #64748b;
            margin-bottom: 12px;
        }

        .query-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 8px;
            font-size: 0.875rem;
            color: #475569;
        }

        .query-item:hover {
            background: #f1f5f9;
            color: #0ea5e9;
            transform: translateX(4px);
        }

        .query-item i {
            width: 16px;
            color: #0ea5e9;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #ffffff;
        }

        .main-header {
            padding: 24px 32px;
            border-bottom: 1px solid #e2e8f0;
            background: #ffffff;
        }

        .main-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .main-header p {
            color: #64748b;
            font-size: 0.875rem;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: calc(100vh - 120px);
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 24px 32px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .message {
            max-width: 80%;
            padding: 16px 20px;
            border-radius: 16px;
            line-height: 1.6;
            position: relative;
        }

        .message.user {
            align-self: flex-end;
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant {
            align-self: flex-start;
            background: #f8fafc;
            color: #334155;
            border: 1px solid #e2e8f0;
            border-bottom-left-radius: 4px;
        }

        .message.system {
            align-self: center;
            background: #dbeafe;
            color: #1e40af;
            font-size: 0.875rem;
            text-align: center;
            border-radius: 20px;
            max-width: 60%;
        }

        .message.error {
            align-self: flex-start;
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .message-time {
            font-size: 0.75rem;
            opacity: 0.6;
            margin-top: 8px;
        }

        .function-call {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 12px;
            margin-top: 12px;
            font-size: 0.75rem;
        }

        .function-call-header {
            font-weight: 600;
            color: #0369a1;
            margin-bottom: 4px;
        }

        .function-call-details {
            color: #0284c7;
            font-family: 'Monaco', 'Menlo', monospace;
        }

        .typing-indicator {
            align-self: flex-start;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 16px 20px;
            border-radius: 16px;
            border-bottom-left-radius: 4px;
            display: none;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #64748b;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .chat-input {
            padding: 24px 32px;
            border-top: 1px solid #e2e8f0;
            background: #ffffff;
            display: flex;
            gap: 16px;
            align-items: flex-end;
        }

        .input-group {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 0.875rem;
            resize: none;
            outline: none;
            transition: all 0.2s ease;
            max-height: 120px;
            min-height: 52px;
            font-family: inherit;
        }

        .message-input:focus {
            border-color: #0ea5e9;
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
        }

        .send-btn {
            width: 52px;
            height: 52px;
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            font-size: 18px;
        }

        .send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(14, 165, 233, 0.3);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .welcome-message {
            text-align: center;
            padding: 40px 20px;
            color: #64748b;
        }

        .welcome-message h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .welcome-message p {
            font-size: 0.875rem;
            line-height: 1.6;
            max-width: 500px;
            margin: 0 auto;
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .sidebar {
                display: none;
            }
            
            .main-content {
                width: 100%;
            }
            
            .message {
                max-width: 90%;
            }
            
            .chat-messages {
                padding: 16px;
            }
            
            .chat-input {
                padding: 16px;
            }
        }

        /* Scrollbar styling */
        .chat-messages::-webkit-scrollbar,
        .sidebar-content::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track,
        .sidebar-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .sidebar-content::-webkit-scrollbar-thumb {
            background: #e2e8f0;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h1><i class="fas fa-hospital"></i> Indici Assistant</h1>
            <p>Inpatient Unit Management System</p>
            <div class="connection-status">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">Connecting...</span>
            </div>
        </div>
        
        <div class="sidebar-content">
            <div class="section">
                <div class="section-title">Patient Management</div>
                <div class="query-item" data-message="Show me current inpatients">
                    <i class="fas fa-users"></i>
                    <span>Show me current inpatients</span>
                </div>
                <div class="query-item" data-message="Admit new patient">
                    <i class="fas fa-user-plus"></i>
                    <span>Admit new patient</span>
                </div>
                <div class="query-item" data-message="Process patient discharge">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Process patient discharge</span>
                </div>
                <div class="query-item" data-message="Approve home leave request">
                    <i class="fas fa-home"></i>
                    <span>Approve home leave request</span>
                </div>
            </div>

            <div class="section">
                <div class="section-title">Bed & Room Management</div>
                <div class="query-item" data-message="Show bed availability">
                    <i class="fas fa-bed"></i>
                    <span>Show bed availability</span>
                </div>
                <div class="query-item" data-message="Assign bed to patient">
                    <i class="fas fa-user-check"></i>
                    <span>Assign bed to patient</span>
                </div>
                <div class="query-item" data-message="Find isolation beds">
                    <i class="fas fa-shield-alt"></i>
                    <span>Find isolation beds</span>
                </div>
                <div class="query-item" data-message="Room transfer request">
                    <i class="fas fa-exchange-alt"></i>
                    <span>Room transfer request</span>
                </div>
            </div>

            <div class="section">
                <div class="section-title">Care Management</div>
                <div class="query-item" data-message="Schedule patient care plan">
                    <i class="fas fa-calendar-plus"></i>
                    <span>Schedule patient care plan</span>
                </div>
                <div class="query-item" data-message="Update treatment status">
                    <i class="fas fa-stethoscope"></i>
                    <span>Update treatment status</span>
                </div>
                <div class="query-item" data-message="Medication administration">
                    <i class="fas fa-pills"></i>
                    <span>Medication administration</span>
                </div>
            </div>

            <div class="section">
                <div class="section-title">Reports & Analytics</div>
                <div class="query-item" data-message="IPU occupancy report">
                    <i class="fas fa-chart-bar"></i>
                    <span>IPU occupancy report</span>
                </div>
                <div class="query-item" data-message="Patient flow analysis">
                    <i class="fas fa-chart-line"></i>
                    <span>Patient flow analysis</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="main-header">
            <h2>Inpatient Unit Management Assistant</h2>
            <p>Manage patient admissions, discharges, bed assignments, care plans, and home leave</p>
        </div>
        
        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message">
                    <h3>Welcome to Indici Assistant! 👋</h3>
                    <p>Your intelligent Inpatient Unit management companion. I can help you manage patient care, admissions, discharges, and all aspects of inpatient operations.</p>
                </div>
            </div>
            
            <div class="typing-indicator" id="typingIndicator">
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
            
            <div class="chat-input">
                <div class="input-group">
                    <textarea 
                        class="message-input" 
                        id="messageInput" 
                        placeholder="Ask me about patient admissions, discharges, bed management, or care plans..."
                        rows="1"
                    ></textarea>
                </div>
                <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Initialize Socket.IO
        const socket = io();
        let sessionId = null;
        let isConnected = false;
        let messageHistory = [];
        let historyIndex = -1;
        let currentDraft = '';

        // DOM elements
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const typingIndicator = document.getElementById('typingIndicator');
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');

        // Socket event handlers
        socket.on('connect', () => {
            console.log('Connected to server');
            isConnected = true;
            updateStatus('Connected', 'connected');
        });

        socket.on('connected', (data) => {
            sessionId = data.session_id;
            console.log('Session established:', sessionId);
            
            // Add welcome message
            addMessage('system', data.message);
            
            // Update status based on capabilities
            updateStatus(data.provider || 'Connected', 'connected');
        });

        socket.on('disconnect', () => {
            console.log('Disconnected from server');
            isConnected = false;
            updateStatus('Disconnected', 'disconnected');
        });

        socket.on('response', (data) => {
            hideTyping();
            addMessage('assistant', data.message, data.timestamp, data.function_call);
        });

        socket.on('error', (data) => {
            hideTyping();
            addMessage('error', data.message);
        });

        socket.on('conversation_cleared', (data) => {
            chatMessages.innerHTML = '<div class="welcome-message"><h3>Conversation Cleared</h3><p>Ready for new queries!</p></div>';
        });

        // UI Functions
        function updateStatus(text, type) {
            statusText.textContent = text;
            
            if (type === 'connected') {
                statusDot.style.background = '#10b981';
            } else if (type === 'disconnected') {
                statusDot.style.background = '#ef4444';
            } else {
                statusDot.style.background = '#f59e0b';
            }
        }

        function addMessage(type, content, timestamp = null, functionCall = null) {
            // Remove welcome message if it exists
            const welcomeMsg = chatMessages.querySelector('.welcome-message');
            if (welcomeMsg) {
                welcomeMsg.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            let messageContent = content;
            
            // Add timestamp if provided
            if (timestamp) {
                const time = new Date(timestamp).toLocaleTimeString();
                messageContent += `<div class="message-time">${time}</div>`;
            }
            
            // Add function call info if provided
            if (functionCall) {
                messageContent += `
                    <div class="function-call">
                        <div class="function-call-header">🔧 Function Called:</div>
                        <div class="function-call-details">${functionCall.name}(${JSON.stringify(functionCall.arguments)})</div>
                    </div>
                `;
            }
            
            messageDiv.innerHTML = messageContent;
            chatMessages.appendChild(messageDiv);
            scrollToBottom();
        }

        function showTyping() {
            typingIndicator.style.display = 'block';
            scrollToBottom();
        }

        function hideTyping() {
            typingIndicator.style.display = 'none';
        }

        function scrollToBottom() {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || !isConnected) return;

            // Add to message history
            if (messageHistory.length === 0 || messageHistory[messageHistory.length - 1] !== message) {
                messageHistory.push(message);
            }
            historyIndex = messageHistory.length;
            currentDraft = '';

            // Add user message to chat
            addMessage('user', message, new Date().toISOString());

            // Clear input
            messageInput.value = '';
            adjustTextareaHeight();

            // Show typing indicator
            showTyping();

            // Send message to server
            socket.emit('message', { message: message });
        }

        function adjustTextareaHeight() {
            messageInput.style.height = 'auto';
            messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
        }

        // Event listeners
        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            } else if (e.key === 'ArrowUp') {
                // Navigate up through message history
                if (messageHistory.length > 0) {
                    // Save current draft if we're at the end of history
                    if (historyIndex === messageHistory.length) {
                        currentDraft = messageInput.value;
                    }

                    // Move up in history (if not at the beginning)
                    if (historyIndex > 0) {
                        historyIndex--;
                        messageInput.value = messageHistory[historyIndex];
                        // Move cursor to end of text
                        setTimeout(() => {
                            messageInput.selectionStart = messageInput.selectionEnd = messageInput.value.length;
                            adjustTextareaHeight();
                        }, 0);
                    }
                }
                e.preventDefault();
            } else if (e.key === 'ArrowDown') {
                // Navigate down through message history
                if (messageHistory.length > 0) {
                    // Move down in history (if not at the end)
                    if (historyIndex < messageHistory.length) {
                        historyIndex++;

                        if (historyIndex === messageHistory.length) {
                            // At the end, restore current draft
                            messageInput.value = currentDraft;
                        } else {
                            messageInput.value = messageHistory[historyIndex];
                        }

                        // Move cursor to end of text
                        setTimeout(() => {
                            messageInput.selectionStart = messageInput.selectionEnd = messageInput.value.length;
                            adjustTextareaHeight();
                        }, 0);
                    }
                }
                e.preventDefault();
            }
        });

        messageInput.addEventListener('input', adjustTextareaHeight);

        // Suggestion click handlers
        document.querySelectorAll('.query-item').forEach(item => {
            item.addEventListener('click', () => {
                const message = item.getAttribute('data-message');
                messageInput.value = message;
                adjustTextareaHeight();
                sendMessage();
            });
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            messageInput.focus();
        });
    </script>
</body>
</html>
