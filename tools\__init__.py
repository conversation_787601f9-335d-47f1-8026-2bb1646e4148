"""
MCP Tools for IPU Management System

This package contains all the MCP tool implementations for interacting
with the IPU Management System API.
"""

from .building_tools import BuildingTools, BuildingDetailsTools, BuildingSearchTools, BuildingStatsTools
from .ward_tools import WardTools, WardsByBuildingTools, WardDetailsTools, WardSearchTools, WardStatsTools
from .room_tools import (
    RoomTools, RoomsByWardTools, AvailableRoomsTools, IsolationRoomsTools,
    HomeCareRoomsTools, RoomSearchTools, RoomDetailsTools
)
from .analytics_tools import OccupancyStatsTools, SystemStatsTools, CapacityAnalysisTools, TrendAnalysisTools

__all__ = [
    'BuildingTools', 'BuildingDetailsTools', 'BuildingSearchTools', 'BuildingStatsTools',
    'WardTools', 'WardsByBuildingTools', 'WardDetailsTools', 'WardSearchTools', 'WardStatsTools',
    'RoomTools', 'RoomsByWardTools', 'AvailableRoomsTools', 'IsolationRoomsTools',
    'HomeCareRoomsTools', 'RoomSearchTools', 'RoomDetailsTools',
    'OccupancyStatsTools', 'SystemStatsTools', 'CapacityAnalysisTools', 'TrendAnalysisTools'
]
