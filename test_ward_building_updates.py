#!/usr/bin/env python3
"""
Test ward and building update functionality
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_ward_building_updates():
    """Test ward and building update functionality"""
    print("🧪 Testing Ward and Building Update Functionality")
    print("=" * 60)
    
    try:
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Test Ward Update
        print("1️⃣ Testing Ward Update:")
        print("-" * 40)
        
        # Get current wards
        wards = await client.get_wards()
        print("Current wards:")
        for ward in wards[:3]:  # Show first 3
            print(f"   - '{ward.get('localityTitle')}'")
        
        if wards:
            test_ward = wards[0].get('localityTitle')
            new_ward_name = f"TestWard{int(asyncio.get_event_loop().time())}"
            
            print(f"\nUpdating ward '{test_ward}' to '{new_ward_name}'")
            
            try:
                result = await asyncio.wait_for(
                    chatbot.process_message('test-session', f'update ward name {test_ward} to {new_ward_name}'),
                    timeout=30.0
                )
                
                print(f"Result type: {result.get('type')}")
                content = result.get('content', '')
                
                # Check for key features
                if result.get('type') == 'success':
                    print("✅ Ward update reported as successful!")
                else:
                    print("❌ Ward update not successful")
                
                if 'Updated Ward List:' in content:
                    print("✅ Ward response includes updated table!")
                else:
                    print("❌ Ward response missing updated table")
                
                if 'Ward ID:' not in content and 'localityID' not in content:
                    print("✅ No IDs shown in ward response!")
                else:
                    print("❌ IDs still visible in ward response")
                
                print(f"\nWard Response Preview:")
                print(content[:300] + "..." if len(content) > 300 else content)
                
            except asyncio.TimeoutError:
                print("⏱️ Ward update timeout")
            except Exception as e:
                print(f"❌ Ward update error: {e}")
        
        # Test Building Update
        print(f"\n2️⃣ Testing Building Update:")
        print("-" * 40)
        
        # Get current buildings
        buildings = await client.get_buildings()
        print("Current buildings:")
        for building in buildings[:3]:  # Show first 3
            print(f"   - '{building.get('localityTitle')}'")
        
        if buildings:
            test_building = buildings[0].get('localityTitle')
            new_building_name = f"TestBuilding{int(asyncio.get_event_loop().time())}"
            
            print(f"\nUpdating building '{test_building}' to '{new_building_name}'")
            
            try:
                result = await asyncio.wait_for(
                    chatbot.process_message('test-session', f'update building name {test_building} to {new_building_name}'),
                    timeout=30.0
                )
                
                print(f"Result type: {result.get('type')}")
                content = result.get('content', '')
                
                # Check for key features
                if result.get('type') == 'success':
                    print("✅ Building update reported as successful!")
                else:
                    print("❌ Building update not successful")
                
                if 'Updated Building List:' in content:
                    print("✅ Building response includes updated table!")
                else:
                    print("❌ Building response missing updated table")
                
                if 'Building ID:' not in content and 'localityID' not in content:
                    print("✅ No IDs shown in building response!")
                else:
                    print("❌ IDs still visible in building response")
                
                print(f"\nBuilding Response Preview:")
                print(content[:300] + "..." if len(content) > 300 else content)
                
            except asyncio.TimeoutError:
                print("⏱️ Building update timeout")
            except Exception as e:
                print(f"❌ Building update error: {e}")
        
        print(f"\n✅ Ward and Building update tests completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_ward_building_updates())
