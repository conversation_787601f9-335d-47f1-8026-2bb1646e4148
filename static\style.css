/* Additional styles for IPU MCP Chatbot Web Interface */

/* Message formatting for better readability */
.message-content pre {
    background: rgba(0,0,0,0.05);
    padding: 10px;
    border-radius: 8px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    margin: 5px 0;
}

.message-content code {
    background: rgba(0,0,0,0.05);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.message-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
    font-size: 0.9rem;
}

.message-content table th,
.message-content table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.message-content table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.message-content table tr:nth-child(even) {
    background-color: #f9f9f9;
}

/* Statistics and data formatting */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin: 10px 0;
}

.stat-item {
    background: rgba(79, 172, 254, 0.1);
    padding: 10px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid rgba(79, 172, 254, 0.2);
}

.stat-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #4facfe;
}

.stat-label {
    font-size: 0.8rem;
    color: #666;
    margin-top: 2px;
}

/* Room and facility cards */
.facility-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin: 8px 0;
}

.facility-card h4 {
    color: #4facfe;
    margin-bottom: 8px;
    font-size: 1rem;
}

.facility-card .facility-details {
    font-size: 0.9rem;
    color: #666;
}

.facility-card .facility-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-top: 5px;
}

.facility-status.available {
    background: #d4edda;
    color: #155724;
}

.facility-status.occupied {
    background: #f8d7da;
    color: #721c24;
}

.facility-status.maintenance {
    background: #fff3cd;
    color: #856404;
}

/* Loading states */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #4facfe;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error and warning message styling */
.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    padding: 10px;
    margin: 10px 0;
}

.warning-message {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 10px;
    margin: 10px 0;
}

.success-message {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 10px;
    margin: 10px 0;
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .facility-card {
        padding: 8px;
    }
    
    .message-content table {
        font-size: 0.8rem;
    }
    
    .message-content table th,
    .message-content table td {
        padding: 6px;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    .message-content pre {
        background: rgba(255,255,255,0.1);
        color: #e9ecef;
    }
    
    .message-content code {
        background: rgba(255,255,255,0.1);
        color: #e9ecef;
    }
    
    .facility-card {
        background: rgba(255,255,255,0.05);
        border-color: rgba(255,255,255,0.1);
    }
    
    .facility-card .facility-details {
        color: #adb5bd;
    }
}

/* Animation for new messages */
.message {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover effects for interactive elements */
.example-query {
    transition: all 0.3s ease;
}

.example-query:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(79, 172, 254, 0.2);
}

/* Custom scrollbar for webkit browsers */
.chat-messages::-webkit-scrollbar {
    width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%);
}

/* Focus states for accessibility */
.chat-input:focus {
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.2);
}

.send-button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.2);
}

.example-query:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(79, 172, 254, 0.5);
}
