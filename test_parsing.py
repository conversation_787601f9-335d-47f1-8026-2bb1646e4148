#!/usr/bin/env python3
"""
Test the parsing logic
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_parsing():
    """Test the parsing logic"""
    print("🧪 Testing Parsing Logic")
    print("=" * 40)
    
    try:
        # Create fresh instances
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Test different parsing scenarios
        test_cases = [
            "update room name Room 1 to NewRoom1",
            "update building name test333 to Buildingone", 
            "change room Building One to NewBuilding",
            "edit room name surg to NewSurg"
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}️⃣ Testing: '{test_case}'")
            
            # Parse the message manually to see what name is extracted
            if " to " in test_case:
                parts = test_case.split(" to ")
                old_name_part = parts[0]
                new_name = parts[1].strip()
                
                words = old_name_part.split()
                command_words = ["update", "room", "building", "name", "change", "modify", "edit", "rename"]
                
                # Find the last command word position
                last_command_pos = -1
                for j, word in enumerate(words):
                    if word.lower() in command_words:
                        last_command_pos = j
                
                # Extract name
                if last_command_pos >= 0 and last_command_pos + 1 < len(words):
                    name_words = words[last_command_pos + 1:]
                    old_name = " ".join(name_words)
                else:
                    old_name = words[-1] if words else "Unknown"
                
                print(f"   Extracted old name: '{old_name}'")
                print(f"   New name: '{new_name}'")
                
                # Test with actual chatbot (but don't wait for API response)
                print(f"   Testing with chatbot...")
                try:
                    result = await asyncio.wait_for(
                        chatbot.process_message('test-session', test_case),
                        timeout=5.0
                    )
                    print(f"   Result type: {result.get('type')}")
                except asyncio.TimeoutError:
                    print(f"   Timeout (API call in progress)")
                except Exception as e:
                    print(f"   Error: {str(e)[:100]}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == '__main__':
    asyncio.run(test_parsing())
