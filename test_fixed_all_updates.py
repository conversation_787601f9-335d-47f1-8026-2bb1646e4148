#!/usr/bin/env python3
"""
Test all fixed update functions
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_fixed_all_updates():
    """Test all fixed update functions"""
    print("🧪 Testing All Fixed Update Functions")
    print("=" * 60)
    
    try:
        # Create fresh instances
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Get current data first
        print("1️⃣ Getting current entities:")
        print("-" * 40)
        
        buildings = await client.get_buildings()
        wards = await client.get_wards()
        rooms = await client.get_rooms()
        
        print(f"Buildings: {len(buildings)}")
        for building in buildings[:3]:
            print(f"   - {building.get('localityTitle')} (Code: {building.get('localityCode')})")
        
        print(f"Wards: {len(wards)}")
        for ward in wards[:3]:
            print(f"   - {ward.get('localityTitle')} (Code: {ward.get('localityCode')})")
        
        print(f"Rooms: {len(rooms)}")
        for room in rooms[:3]:
            print(f"   - {room.get('localityTitle')} (Code: {room.get('localityCode')})")
        
        # Test building update
        if buildings:
            print(f"\n2️⃣ Testing building update:")
            print("-" * 40)
            
            test_building = buildings[0]['localityTitle']
            new_building_name = f"UpdatedBuilding{int(asyncio.get_event_loop().time())}"
            
            print(f"Updating '{test_building}' to '{new_building_name}'")
            
            try:
                result = await asyncio.wait_for(
                    chatbot.process_message('test-session', f'update building name {test_building} to {new_building_name}'),
                    timeout=15.0
                )
                
                print(f"Result type: {result.get('type')}")
                if result.get('type') == 'success':
                    print(f"✅ Building update SUCCESS!")
                    print(f"Response: {result.get('content', '')[:200]}...")
                else:
                    print(f"❌ Building update FAILED:")
                    print(f"Response: {result.get('content', '')[:300]}...")
                    
            except asyncio.TimeoutError:
                print(f"⏱️ Building update timeout")
            except Exception as e:
                print(f"❌ Building update error: {e}")
        
        # Test ward update
        if wards:
            print(f"\n3️⃣ Testing ward update:")
            print("-" * 40)
            
            test_ward = wards[0]['localityTitle']
            new_ward_name = f"UpdatedWard{int(asyncio.get_event_loop().time())}"
            
            print(f"Updating '{test_ward}' to '{new_ward_name}'")
            
            try:
                result = await asyncio.wait_for(
                    chatbot.process_message('test-session', f'update ward name {test_ward} to {new_ward_name}'),
                    timeout=15.0
                )
                
                print(f"Result type: {result.get('type')}")
                if result.get('type') == 'success':
                    print(f"✅ Ward update SUCCESS!")
                    print(f"Response: {result.get('content', '')[:200]}...")
                else:
                    print(f"❌ Ward update FAILED:")
                    print(f"Response: {result.get('content', '')[:300]}...")
                    
            except asyncio.TimeoutError:
                print(f"⏱️ Ward update timeout")
            except Exception as e:
                print(f"❌ Ward update error: {e}")
        
        # Test room update
        if rooms:
            print(f"\n4️⃣ Testing room update:")
            print("-" * 40)
            
            test_room = rooms[0]['localityTitle']
            new_room_name = f"UpdatedRoom{int(asyncio.get_event_loop().time())}"
            
            print(f"Updating '{test_room}' to '{new_room_name}'")
            
            try:
                result = await asyncio.wait_for(
                    chatbot.process_message('test-session', f'update room name {test_room} to {new_room_name}'),
                    timeout=15.0
                )
                
                print(f"Result type: {result.get('type')}")
                if result.get('type') == 'success':
                    print(f"✅ Room update SUCCESS!")
                    print(f"Response: {result.get('content', '')[:200]}...")
                else:
                    print(f"❌ Room update FAILED:")
                    print(f"Response: {result.get('content', '')[:300]}...")
                    
            except asyncio.TimeoutError:
                print(f"⏱️ Room update timeout")
            except Exception as e:
                print(f"❌ Room update error: {e}")
        
        print(f"\n✅ All tests completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_fixed_all_updates())
