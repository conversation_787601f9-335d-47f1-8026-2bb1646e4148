#!/usr/bin/env python3
"""
Test specific queries to verify functionality
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_specific_queries():
    """Test specific queries"""
    print("🧪 Testing Specific Queries")
    print("=" * 60)
    
    try:
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Get actual data first
        buildings = await client.get_buildings()
        wards = await client.get_wards()
        
        print("Available buildings:")
        for building in buildings[:5]:
            print(f"  - '{building.get('localityTitle')}'")
        
        print("\nAvailable wards:")
        for ward in wards[:5]:
            print(f"  - '{ward.get('localityTitle')}' (Building: {ward.get('parentLocalityTitle', 'N/A')})")
        
        # Test queries
        test_queries = [
            "please give me list of all wards of building demo785",
            "show me wards in demo785",
            "list wards of demo785",
            "rooms in Surgical Ward",
            "show me rooms of Surgical Ward",
            "list rooms in Surgical Ward",
            "show me available rooms",
            "list available rooms",
            "available rooms"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{i}️⃣ Testing: '{query}'")
            print("-" * 50)
            
            try:
                result = await asyncio.wait_for(
                    chatbot.process_message('test-session', query),
                    timeout=25.0
                )
                
                print(f"Result type: {result.get('type')}")
                content = result.get('content', '')
                
                if result.get('type') == 'success':
                    print("✅ Query successful!")
                    # Check if it contains table
                    if '<table' in content:
                        print("✅ Contains table!")
                    else:
                        print("❌ No table found")
                else:
                    print("❌ Query failed")
                
                print(f"Response preview: {content[:150]}...")
                
            except asyncio.TimeoutError:
                print("⏱️ Query timeout")
            except Exception as e:
                print(f"❌ Query error: {e}")
        
        print(f"\n✅ Specific queries test completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_specific_queries())
