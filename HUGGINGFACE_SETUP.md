# Hugging Face Setup Guide

## Quick Setup for Local LLM

### 1. Install Dependencies
```bash
pip install transformers torch tokenizers accelerate
```

### 2. Set Environment Variables
```bash
# Use Hugging Face instead of API-based models
export USE_HUGGINGFACE=true

# Optional: Specify a different model
export HUGGINGFACE_MODEL=microsoft/DialoGPT-medium
```

### 3. Available Models

#### Recommended Models:
- **microsoft/DialoGPT-medium** (Default) - Good balance of quality and speed
- **microsoft/DialoGPT-large** - Better quality, slower
- **facebook/blenderbot-400M-distill** - Fast, good for basic conversations
- **microsoft/DialoGPT-small** - Fastest, basic quality

#### Advanced Models (Require more resources):
- **microsoft/DialoGPT-large** - High quality conversations
- **facebook/blenderbot-1B-distill** - Better understanding
- **EleutherAI/gpt-neo-1.3B** - More general knowledge

### 4. GPU Support (Optional)
If you have a CUDA-compatible GPU:
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 5. Start the Application
```bash
python start_indici.py
```

## Model Comparison

| Model | Size | Speed | Quality | Memory |
|-------|------|-------|---------|---------|
| DialoGPT-small | 117M | Fast | Basic | 1GB |
| DialoGPT-medium | 345M | Medium | Good | 2GB |
| DialoGPT-large | 762M | Slow | High | 4GB |
| BlenderBot-400M | 400M | Fast | Good | 2GB |

## Troubleshooting

### Common Issues:
1. **Out of Memory** - Use a smaller model
2. **Slow Performance** - Enable GPU or use smaller model
3. **Model Download Fails** - Check internet connection

### Performance Tips:
- Use GPU if available
- Start with smaller models
- Close other applications to free memory

## Windows Setup
```cmd
set USE_HUGGINGFACE=true
set HUGGINGFACE_MODEL=microsoft/DialoGPT-medium
python start_indici.py
```

## Linux/Mac Setup
```bash
export USE_HUGGINGFACE=true
export HUGGINGFACE_MODEL=microsoft/DialoGPT-medium
python start_indici.py
```
