"""
Ward management tools for MCP
"""

from typing import Any, Dict, List
from mcp.types import Tool, TextContent
from .base_tool import BaseTool


class WardTools(BaseTool):
    """Tools for ward management"""
    
    def get_tool_definition(self) -> Tool:
        """Get wards tool definition"""
        return Tool(
            name="get_wards",
            description="Get all wards in the IPU system",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute get wards"""
        wards = await self.client.get_wards()

        if not wards:
            return [TextContent(type="text", text="🏥 No wards found in the system.")]

        # Format wards using actual API field names
        text = "🏥 **Hospital Wards**\n\n"
        for ward in wards:
            text += f"**{ward.get('localityTitle', 'Unknown')}** (ID: {ward.get('localityID')})\n"
            text += f"• Code: {ward.get('localityCode', 'N/A')}\n"
            if ward.get('localityDescription'):
                text += f"• Description: {ward.get('localityDescription')}\n"
            text += f"• Rooms: {ward.get('childCount', 0)}\n"
            text += f"• Building: {ward.get('parentLocalityTitle', 'N/A')}\n"
            text += f"• Status: {'Active' if ward.get('isActive', False) else 'Inactive'}\n"
            if ward.get('facilities'):
                text += f"• Facilities: {ward.get('facilities')}\n"
            text += "\n"

        return [TextContent(type="text", text=text)]


class WardsByBuildingTools(BaseTool):
    """Tools for getting wards by building"""
    
    def get_tool_definition(self) -> Tool:
        """Get wards by building tool definition"""
        return Tool(
            name="get_wards_by_building",
            description="Get all wards in a specific building",
            inputSchema={
                "type": "object",
                "properties": {
                    "building_id": {
                        "type": "integer",
                        "description": "The ID of the building to get wards for"
                    }
                },
                "required": ["building_id"]
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute get wards by building"""
        # Validate arguments
        error = self.validate_required_args(arguments, ["building_id"])
        if error:
            return self.create_error_response(error)
        
        error = self.validate_integer_arg(arguments, "building_id")
        if error:
            return self.create_error_response(error)
        
        building_id = int(arguments["building_id"])
        
        # Get building info first
        building = await self.client.get_building_by_id(building_id)
        if not building:
            return [TextContent(type="text", text=f"🏢 Building with ID {building_id} not found.")]
        
        # Get wards
        wards = await self.client.get_wards_by_building(building_id)
        
        if not wards:
            building_name = building.get('title', f'Building {building_id}')
            return [TextContent(type="text", text=f"🏥 No wards found in {building_name}.")]
        
        # Format results
        building_name = building.get('title', f'Building {building_id}')
        summary = self.format_summary_response(
            wards,
            ["title", "code", "description"],
            f"🏥 Wards in {building_name}"
        )
        
        return [TextContent(type="text", text=summary)]


class WardDetailsTools(BaseTool):
    """Tools for getting ward details"""
    
    def get_tool_definition(self) -> Tool:
        """Get ward details tool definition"""
        return Tool(
            name="get_ward_details",
            description="Get detailed information about a specific ward by ID",
            inputSchema={
                "type": "object",
                "properties": {
                    "ward_id": {
                        "type": "integer",
                        "description": "The ID of the ward to retrieve"
                    }
                },
                "required": ["ward_id"]
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute get ward details"""
        # Validate arguments
        error = self.validate_required_args(arguments, ["ward_id"])
        if error:
            return self.create_error_response(error)
        
        error = self.validate_integer_arg(arguments, "ward_id")
        if error:
            return self.create_error_response(error)
        
        ward_id = int(arguments["ward_id"])
        ward = await self.client.get_ward_by_id(ward_id)
        
        if not ward:
            return [TextContent(type="text", text=f"🏥 Ward with ID {ward_id} not found.")]
        
        # Get room count for this ward
        try:
            rooms = await self.client.get_rooms_by_ward(ward_id)
            room_count = len(rooms)
            occupied_rooms = sum(1 for room in rooms if room.get('isOccupied', False))
        except Exception:
            room_count = 0
            occupied_rooms = 0
        
        # Format ward details
        details = f"""🏥 **Ward Details**

**Name:** {ward.get('title', 'N/A')}
**Code:** {ward.get('code', 'N/A')}
**Building:** {ward.get('buildingTitle', 'N/A')}
**Description:** {ward.get('description', 'No description available')}
**Status:** {'Active' if ward.get('isActive', False) else 'Inactive'}
**Total Rooms:** {room_count}
**Occupied Rooms:** {occupied_rooms}
**Available Rooms:** {room_count - occupied_rooms}
**Created:** {ward.get('createdAt', 'N/A')}
**Last Updated:** {ward.get('updatedAt', 'Never')}
"""
        
        return [TextContent(type="text", text=details)]


class WardSearchTools(BaseTool):
    """Tools for searching wards"""
    
    def get_tool_definition(self) -> Tool:
        """Get ward search tool definition"""
        return Tool(
            name="search_wards",
            description="Search for wards by name, code, or description",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query to find wards"
                    }
                },
                "required": ["query"]
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute ward search"""
        # Validate arguments
        error = self.validate_required_args(arguments, ["query"])
        if error:
            return self.create_error_response(error)
        
        query = arguments["query"].strip()
        if not query:
            return self.create_error_response("Search query cannot be empty")
        
        # Get all wards and filter
        wards = await self.client.get_wards()
        query_lower = query.lower()
        
        filtered_wards = [
            ward for ward in wards
            if (query_lower in ward.get('title', '').lower() or
                query_lower in ward.get('code', '').lower() or
                query_lower in ward.get('description', '').lower() or
                query_lower in ward.get('buildingTitle', '').lower())
        ]
        
        if not filtered_wards:
            return [TextContent(type="text", text=f"🔍 No wards found matching '{query}'.")]
        
        # Format results
        summary = self.format_summary_response(
            filtered_wards,
            ["title", "code", "buildingTitle"],
            f"🔍 Wards matching '{query}'"
        )
        
        return [TextContent(type="text", text=summary)]


class WardStatsTools(BaseTool):
    """Tools for ward statistics"""
    
    def get_tool_definition(self) -> Tool:
        """Get ward stats tool definition"""
        return Tool(
            name="get_ward_stats",
            description="Get statistics about wards in the system",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute ward stats"""
        wards = await self.client.get_wards()
        
        if not wards:
            return [TextContent(type="text", text="📊 No wards found to analyze.")]
        
        # Calculate stats
        total_wards = len(wards)
        active_wards = sum(1 for w in wards if w.get('isActive', False))
        inactive_wards = total_wards - active_wards
        
        # Group by building
        building_ward_counts = {}
        for ward in wards:
            building = ward.get('buildingTitle', 'Unknown Building')
            building_ward_counts[building] = building_ward_counts.get(building, 0) + 1
        
        # Get room counts for each ward
        ward_room_counts = []
        for ward in wards[:10]:  # Limit to first 10 for performance
            try:
                rooms = await self.client.get_rooms_by_ward(ward['id'])
                ward_room_counts.append({
                    'ward': ward.get('title', 'Unknown'),
                    'room_count': len(rooms)
                })
            except Exception:
                ward_room_counts.append({
                    'ward': ward.get('title', 'Unknown'),
                    'room_count': 0
                })
        
        # Format stats
        stats = f"""📊 **Ward Statistics**

**Total Wards:** {total_wards}
**Active Wards:** {active_wards}
**Inactive Wards:** {inactive_wards}

**Wards by Building:**
"""
        
        for building, count in building_ward_counts.items():
            stats += f"• {building}: {count} wards\n"
        
        if ward_room_counts:
            stats += "\n**Top Wards by Room Count:**\n"
            sorted_wards = sorted(ward_room_counts, key=lambda x: x['room_count'], reverse=True)
            for item in sorted_wards[:5]:
                stats += f"• {item['ward']}: {item['room_count']} rooms\n"
        
        return [TextContent(type="text", text=stats)]


class UpdateWardByNameTools(BaseTool):
    """Tools for updating ward by name"""

    def get_tool_definition(self) -> Tool:
        """Get update ward by name tool definition"""
        return Tool(
            name="update_ward_by_name",
            description="Update ward information by name using the new API endpoint",
            inputSchema={
                "type": "object",
                "properties": {
                    "ward_name": {
                        "type": "string",
                        "description": "The current name of the ward to update"
                    },
                    "new_name": {
                        "type": "string",
                        "description": "New ward name (localityTitle)"
                    },
                    "code": {
                        "type": "string",
                        "description": "New ward code (localityCode)"
                    },
                    "description": {
                        "type": "string",
                        "description": "New ward description (localityDescription)"
                    },
                    "facilities": {
                        "type": "string",
                        "description": "New facilities information"
                    }
                },
                "required": ["ward_name"]
            }
        )

    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute update ward by name"""
        ward_name = arguments.get("ward_name")

        if not ward_name:
            return [TextContent(type="text", text="❌ Ward name is required")]

        # Prepare update data using UpdateLocalityDto format
        update_data = {}
        if arguments.get("new_name"):
            update_data["localityTitle"] = arguments["new_name"]
        if arguments.get("code"):
            update_data["localityCode"] = arguments["code"]
        if arguments.get("description"):
            update_data["localityDescription"] = arguments["description"]
        if arguments.get("facilities"):
            update_data["facilities"] = arguments["facilities"]

        if not update_data:
            return [TextContent(type="text", text="❌ At least one field must be provided for update")]

        try:
            # Update the ward by name using the new API endpoint
            updated_ward = await self.client.update_ward_by_name(ward_name, update_data)

            # Format success response
            text = f"✅ **Ward Updated Successfully**\n\n"
            text += f"**Previous Name:** {ward_name}\n"
            text += f"**Current Name:** {updated_ward.get('localityTitle', 'N/A')}\n"
            text += f"**Code:** {updated_ward.get('localityCode', 'N/A')}\n"
            text += f"**Description:** {updated_ward.get('localityDescription', 'N/A')}\n"
            text += f"**Building:** {updated_ward.get('parentLocalityTitle', 'N/A')}\n"
            if updated_ward.get('facilities'):
                text += f"**Facilities:** {updated_ward.get('facilities')}\n"
            text += f"**Ward ID:** {updated_ward.get('localityID', 'N/A')}\n"
            text += f"**Last Updated:** {updated_ward.get('updatedAt', 'Now')}\n"

            return [TextContent(type="text", text=text)]

        except Exception as e:
            return [TextContent(type="text", text=f"❌ Error updating ward '{ward_name}': {str(e)}")]
