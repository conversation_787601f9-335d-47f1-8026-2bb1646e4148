#!/usr/bin/env python3
"""
Test the fixed verification functionality
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_fixed_verification():
    """Test the fixed verification functionality"""
    print("🧪 Testing Fixed Verification Functionality")
    print("=" * 60)
    
    try:
        # Get current rooms
        client = create_client()
        rooms = await client.get_rooms()
        
        print("1️⃣ Current rooms:")
        for room in rooms:
            print(f"   - '{room.get('localityTitle')}' (ID: {room.get('localityID')})")
        
        if not rooms:
            print("❌ No rooms found")
            return
        
        # Find a room to test with
        test_room = rooms[0].get('localityTitle')
        new_name = f"VerificationTest{int(asyncio.get_event_loop().time())}"
        
        print(f"\n2️⃣ Testing update with verification:")
        print(f"   Updating '{test_room}' to '{new_name}'")
        
        # Test the chatbot with the new verification logic
        chatbot = create_llm_chatbot('mock', client)
        
        try:
            result = await asyncio.wait_for(
                chatbot.process_message('test-session', f'update room name {test_room} to {new_name}'),
                timeout=30.0
            )
            
            print(f"   Result type: {result.get('type')}")
            print(f"   Content: {result.get('content', '')[:400]}...")
            
            if result.get('type') == 'success':
                print(f"   ✅ Chatbot correctly reported SUCCESS!")
                print(f"   🎉 The verification fix is working!")
            else:
                print(f"   ❌ Chatbot still reporting error")
                
                # Check if the update actually worked
                updated_rooms = await client.get_rooms()
                found_updated = False
                for room in updated_rooms:
                    if room.get('localityTitle') == new_name:
                        found_updated = True
                        break
                
                if found_updated:
                    print(f"   🐛 Update worked but chatbot still reports error - verification logic needs more work")
                else:
                    print(f"   ✅ Update genuinely failed - error reporting is correct")
                
        except asyncio.TimeoutError:
            print(f"   ⏱️ Timeout")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print(f"\n✅ Verification test completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_fixed_verification())
