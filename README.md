# 🏥 Indici Assistant - Professional LLM Chatbot for IPU Management

A sophisticated **LLM-powered AI chatbot** for Inpatient Unit (IPU) management with real-time conversation, IPU-API integration, and professional healthcare interface. Features OpenAI GPT-4/Anthropic Claude integration, MCP (Model Context Protocol) server, and modern web interface for natural language IPU operations.

## 🤖 **NEW: Professional LLM Chatbot**

### **Real AI-Powered Conversations**
- ✅ **OpenAI GPT-4 & Anthropic Claude** - Professional AI responses
- ✅ **Natural Language Processing** - Conversational IPU management
- ✅ **Context Memory** - Remembers conversation history
- ✅ **Function Calling** - Real-time IPU-API integration
- ✅ **Healthcare Communication** - Professional medical terminology

### **Modern Web Interface**
- ✅ **Real-time Chat** - WebSocket-based instant messaging
- ✅ **Professional Design** - Indici-inspired healthcare theme
- ✅ **Mobile Responsive** - Works on all devices
- ✅ **Quick Actions** - Sidebar with common IPU operations
- ✅ **Multi-user Support** - Isolated conversation sessions

### **🚀 Quick Start LLM Chatbot**
```bash
# Set your API key (optional - works without for demo)
export OPENAI_API_KEY="your_openai_key"

# Start professional LLM chatbot
python llm_web_app.py

# Open browser
http://localhost:5000
```

---

## 🏥 Core Features

### Inpatient Unit Management Tools
- **Patient Management**: Admit patients, process discharges, manage transfers
- **Home Leave Processing**: Approve and track patient home leave requests
- **Bed & Room Management**: Real-time bed availability, assignments, and transfers
- **Care Coordination**: Manage care plans, treatments, and medication schedules
- **IPU Analytics**: Occupancy reports, length of stay, and operational metrics
- **Clinical Workflows**: Support for nursing and physician workflows

### Intelligent Assistant Interface
- **Natural Language Processing**: Ask questions in plain English
- **Modern Web Interface**: Indici-inspired design with sidebar navigation
- **Real-time Updates**: Live data from IPU Management System API
- **Multi-modal Access**: Web interface, CLI, and Claude Desktop integration
- **Role-based Queries**: Tailored responses for nurses, doctors, and administrators

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- IPU Management System API running (default: http://localhost:5077)

### Installation
```bash
git clone <repository-url>
cd IPU-MCP-Chatbot
pip install -r requirements.txt
```

### Run MCP Server
```bash
python mcp_server.py
```

### Run Web Interface (Recommended)
```bash
python start_web.py
```
Opens at: http://localhost:5000

### Run Interactive Chatbot (CLI)
```bash
python chatbot.py
```

## 📋 Available Commands

### Patient Management Commands
- "Show me current inpatients"
- "Admit new patient"
- "Process patient discharge"
- "Approve home leave request"
- "Patient transfer request"

### Bed & Room Management
- "Show bed availability"
- "Assign bed to patient"
- "Find isolation beds"
- "Room transfer request"
- "Bed utilization report"

### Care Coordination Commands
- "Schedule patient care plan"
- "Update treatment status"
- "Medication administration"
- "Patient progress notes"
- "Care plan review"

### Analytics & Reports
- "IPU occupancy report"
- "Patient length of stay"
- "Discharge summary report"
- "Daily operations report"
- "System statistics"

## 🔧 Configuration

Edit `config.py` to configure:
- IPU API base URL
- Logging levels
- Chatbot settings
- MCP server options

## 📁 Project Structure

```
IPU-MCP-Chatbot/
├── mcp_server.py          # MCP protocol server
├── web_app.py             # Web interface (Flask + SocketIO)
├── start_web.py           # Web interface launcher
├── chatbot.py             # Interactive CLI chatbot
├── ipu_client.py          # IPU API client
├── templates/             # HTML templates for web interface
├── static/                # CSS and static files
├── tools/                 # MCP tool implementations
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── tests/                 # Unit tests
└── docs/                  # Documentation
```

## 🧪 Testing

```bash
# Run unit tests
python -m pytest tests/

# Test MCP server
python test_mcp.py

# Test chatbot
python test_chatbot.py
```

## 📖 Documentation

- [MCP Server Guide](docs/mcp-server.md)
- [Chatbot Usage](docs/chatbot-usage.md)
- [API Integration](docs/api-integration.md)
- [Development Guide](docs/development.md)

## 🔒 Security

- Read-only access to IPU API
- No patient data access
- Secure API communication
- Audit logging included

## 📞 Support

For issues and questions:
1. Check the documentation in `docs/`
2. Review logs for error details
3. Test API connectivity
4. Verify configuration settings

## 🎯 Use Cases

- **Nurses**: "Show me patients ready for medication administration"
- **Doctors**: "Which patients need discharge planning today?"
- **Charge Nurses**: "What's our current bed availability?"
- **Case Managers**: "Process home leave request for patient in bed A101"
- **Administrators**: "Generate daily IPU occupancy report"
- **Unit Coordinators**: "Assign bed to new admission"

Transform your inpatient unit operations with intelligent AI assistance! 🏥✨
