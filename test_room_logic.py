#!/usr/bin/env python3
"""
Test the updated room logic and generic error handling
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_room_queries():
    """Test different room queries"""
    print("🧪 Testing Updated Room Logic and Generic Error Handling")
    print("=" * 70)
    
    try:
        # Create client and chatbot
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Test 1: All rooms query
        print("\n1️⃣ Testing 'Show me all rooms':")
        print("-" * 40)
        result1 = await chatbot.process_message('test-session', 'Show me all rooms')
        print(f"Type: {result1.get('type')}")
        print(f"Has function_call: {'function_call' in result1}")
        print("Content preview:", result1.get('content', '')[:200] + "...")
        
        # Test 2: Available rooms query
        print("\n2️⃣ Testing 'Show me available rooms':")
        print("-" * 40)
        result2 = await chatbot.process_message('test-session', 'Show me available rooms')
        print(f"Type: {result2.get('type')}")
        print(f"Has function_call: {'function_call' in result2}")
        print("Content preview:", result2.get('content', '')[:200] + "...")
        
        # Test 3: Buildings query (should not show function name)
        print("\n3️⃣ Testing 'Show me all buildings':")
        print("-" * 40)
        result3 = await chatbot.process_message('test-session', 'Show me all buildings')
        print(f"Type: {result3.get('type')}")
        print(f"Has function_call: {'function_call' in result3}")
        print("Content preview:", result3.get('content', '')[:200] + "...")
        
        # Test 4: Generic error handling
        print("\n4️⃣ Testing generic error handling:")
        print("-" * 40)
        
        error_queries = [
            "Show me patient admissions",
            "Schedule a treatment",
            "Generate a report",
            "Manage medications",
            "Transfer a patient"
        ]
        
        for query in error_queries:
            result = await chatbot.process_message('test-session', query)
            print(f"Query: '{query}'")
            print(f"Response: {result.get('content', '')[:100]}...")
            print()
        
        print("✅ All tests completed!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")

if __name__ == '__main__':
    asyncio.run(test_room_queries())
