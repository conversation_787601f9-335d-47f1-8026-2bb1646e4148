#!/usr/bin/env python3
"""
Test the new API features: delete and create operations
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_new_api_features():
    """Test the new API features"""
    print("🧪 Testing New API Features")
    print("=" * 60)
    
    try:
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Test queries for new features
        test_queries = [
            ("create building TestBuilding123", "Create building"),
            ("create ward TestWard123", "Create ward"),
            ("create room TestRoom123", "Create room"),
            ("delete room TestRoom123", "Delete room"),
            ("delete ward TestWard123", "Delete ward"),
        ]
        
        for i, (query, description) in enumerate(test_queries, 1):
            print(f"\n{i}️⃣ Testing {description}: '{query}'")
            print("-" * 50)
            
            try:
                result = await asyncio.wait_for(
                    chatbot.process_message('test-session', query),
                    timeout=20.0
                )
                
                print(f"Result type: {result.get('type')}")
                content = result.get('content', '')
                
                if result.get('type') == 'success':
                    print("✅ Query successful!")
                    
                    # Check specific content
                    if 'created successfully' in content.lower():
                        print("✅ Shows successful creation!")
                    elif 'deleted successfully' in content.lower():
                        print("✅ Shows successful deletion!")
                    elif 'updated successfully' in content.lower():
                        print("✅ Shows successful update!")
                    else:
                        print("? Unknown success type")
                else:
                    print("❌ Query failed")
                
                # Show first line of response
                first_line = content.split('\n')[0] if content else "No content"
                print(f"Response: {first_line}")
                
            except asyncio.TimeoutError:
                print("⏱️ Query timeout")
            except Exception as e:
                print(f"❌ Query error: {str(e)[:100]}...")
        
        # Test direct API calls
        print(f"\n🔧 Testing Direct API Calls:")
        print("-" * 50)
        
        try:
            # Test create building simple
            building_data = {
                "localityTitle": "DirectTestBuilding",
                "localityCode": "DTB123",
                "localityDescription": "Direct test building",
                "isActive": True
            }
            result = await client.create_building_simple(building_data)
            print(f"✅ Direct create building: {result.get('success', False)}")
            
        except Exception as e:
            print(f"❌ Direct create building error: {str(e)[:50]}...")
        
        try:
            # Test delete room by name
            result = await client.delete_room_by_name("NonExistentRoom")
            print(f"✅ Direct delete room: {result.get('success', False)}")
            
        except Exception as e:
            print(f"❌ Direct delete room error: {str(e)[:50]}...")
        
        print(f"\n✅ New API features test completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_new_api_features())
