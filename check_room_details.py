#!/usr/bin/env python3
"""
Check room details
"""

import asyncio
import json
import httpx
from ipu_client import create_client

async def check_room_details():
    """Check room details"""
    print("🔍 Checking Room Details")
    print("=" * 50)
    
    try:
        # Get rooms using the client
        client = create_client()
        rooms = await client.get_rooms()
        
        print(f"Found {len(rooms)} rooms:")
        for room in rooms:
            print(f"  - '{room.get('localityTitle')}' (ID: {room.get('localityID')}, Code: {room.get('localityCode')})")
        
        # Check if roomone exists
        roomone = None
        for room in rooms:
            if room.get('localityTitle') == 'roomone':
                roomone = room
                break
        
        if roomone:
            print(f"\n✅ Found room 'roomone':")
            print(f"  ID: {roomone.get('localityID')}")
            print(f"  Code: {roomone.get('localityCode')}")
            print(f"  Ward: {roomone.get('parentLocalityTitle')}")
            print(f"  Is Active: {roomone.get('isActive')}")
            print(f"  Is Occupied: {roomone.get('isOccupied')}")
        else:
            print(f"\n❌ Room 'roomone' not found")
        
        # Test direct API call to update roomone
        print(f"\nTesting direct API call to update room:")
        
        # Try different room names
        test_rooms = ['roomone', 'Roomone', 'ROOMONE', 'room one', 'Room One']
        
        for test_room in test_rooms:
            print(f"\nTrying to update '{test_room}':")
            
            update_data = {
                "localityTitle": "Room1",
                "isActive": True,
                "localityCode": "R1"
            }
            
            async with httpx.AsyncClient() as http_client:
                try:
                    response = await http_client.put(
                        f"http://localhost:5077/api/Configuration/rooms/update-by-name/{test_room}",
                        json=update_data,
                        timeout=10.0
                    )
                    
                    print(f"  Status: {response.status_code}")
                    if response.status_code == 200:
                        print(f"  ✅ Success!")
                        result = response.json()
                        print(f"  Updated room: {result.get('data', {}).get('localityTitle')}")
                        break
                    else:
                        print(f"  ❌ Failed: {response.text[:200]}")
                        
                except Exception as e:
                    print(f"  ❌ Error: {e}")
        
        # Check if the API is case-sensitive
        print(f"\nChecking if API is case-sensitive:")
        
        # Get a room that definitely exists
        if rooms:
            test_room = rooms[0].get('localityTitle')
            
            # Try with different case
            test_variations = [
                test_room,
                test_room.lower(),
                test_room.upper(),
                test_room.capitalize()
            ]
            
            for variation in test_variations:
                print(f"\nTrying to get room '{variation}':")
                
                async with httpx.AsyncClient() as http_client:
                    try:
                        response = await http_client.get(
                            f"http://localhost:5077/api/Configuration/rooms/by-name/{variation}",
                            timeout=5.0
                        )
                        
                        print(f"  Status: {response.status_code}")
                        if response.status_code == 200:
                            print(f"  ✅ Success!")
                        else:
                            print(f"  ❌ Failed: {response.text[:100]}")
                            
                    except Exception as e:
                        print(f"  ❌ Error: {e}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(check_room_details())
