# 🔧 **API Integration Guide - Adding New Functionality**

This guide explains how to add new API endpoints and functionality to the IPU MCP Chatbot system.

## 📋 **Table of Contents**
1. [Overview](#overview)
2. [Step-by-Step Integration Process](#step-by-step-integration-process)
3. [File Structure](#file-structure)
4. [Code Examples](#code-examples)
5. [Natural Language Patterns](#natural-language-patterns)
6. [Testing](#testing)

---

## 🎯 **Overview**

To add new API functionality to the chatbot, you need to modify 3 main files:
- **`ipu_client.py`** - Add API client methods
- **`llm_service.py`** - Add function registry and message handling
- **Test files** - Create tests for the new functionality

---

## 🚀 **Step-by-Step Integration Process**

### **Step 1: Add API Client Method (`ipu_client.py`)**

Add the new API client method to handle the HTTP request:

```python
async def your_new_function(self, param1: str, param2: Dict[str, Any] = None) -> Dict[str, Any]:
    """Description of what this function does"""
    try:
        # For GET requests
        response = await self._make_request("GET", f"/api/Configuration/your-endpoint/{param1}")
        
        # For POST requests with data
        response = await self._make_request("POST", "/api/Configuration/your-endpoint", json=param2)
        
        # For PUT requests
        response = await self._make_request("PUT", f"/api/Configuration/your-endpoint/{param1}", json=param2)
        
        # For DELETE requests
        response = await self._make_request("DELETE", f"/api/Configuration/your-endpoint/{param1}")
        
        return response  # or self._extract_data(response) for data extraction
    except Exception as e:
        logger.error(f"Error in your_new_function: {e}")
        raise
```

### **Step 2: Register Function (`llm_service.py`)**

Add the function to the function registry (around line 168-251):

```python
"your_new_function": {
    "name": "your_new_function",
    "description": "Description of what this function does",
    "parameters": {
        "type": "object",
        "properties": {
            "param1": {
                "type": "string",
                "description": "Description of parameter 1"
            },
            "param2": {
                "type": "object", 
                "description": "Description of parameter 2"
            }
        },
        "required": ["param1"]  # List required parameters
    }
},
```

### **Step 3: Add Execution Logic (`llm_service.py`)**

Add the execution logic (around line 397-410):

```python
elif function_name == "your_new_function":
    result = await self.ipu_client.your_new_function(
        arguments["param1"], 
        arguments.get("param2")
    )
```

### **Step 4: Add Message Handling (`llm_service.py`)**

Add natural language processing (around line 1308+):

```python
# Handle your new functionality
elif any(keyword in user_lower for keyword in ["your", "keywords", "here"]):
    # Extract parameters from user message
    import re
    patterns = [
        r'your regex pattern here to extract (\w+)',
        r'alternative pattern (\w+) with (\w+)'
    ]
    
    param1 = None
    param2 = None
    
    for pattern in patterns:
        match = re.search(pattern, user_message, re.IGNORECASE)
        if match:
            param1 = match.group(1)
            param2 = match.group(2) if match.lastindex >= 2 else None
            break
    
    if param1 and self.function_registry:
        try:
            result = await self.function_registry.execute_function("your_new_function", {
                "param1": param1,
                "param2": param2
            })
            
            if result.get("success"):
                return {
                    "content": f"""✅ **Operation Successful**

**Parameter 1:** {param1}
**Parameter 2:** {param2}

The operation completed successfully.""",
                    "type": "success"
                }
            else:
                return {
                    "content": f"""❌ **Operation Failed**

Error: {result.get('message', 'Operation failed')}""",
                    "type": "error"
                }
        except Exception as e:
            return {
                "content": f"❌ Error: {str(e)}",
                "type": "error"
            }
```

---

## 📁 **File Structure**

```
IPU-MCP-Chatbot/
├── ipu_client.py          # ← Add API client methods here
├── llm_service.py         # ← Add function registry & message handling
├── test_your_feature.py   # ← Create test file
└── API_INTEGRATION_GUIDE.md  # ← This guide
```

---

## 💡 **Code Examples**

### **Example 1: Patient Management**

If you add patient endpoints to your API:

**1. API Client (`ipu_client.py`):**
```python
async def get_patients(self) -> List[Dict[str, Any]]:
    """Get all patients"""
    try:
        response = await self._make_request("GET", "/api/Configuration/patients")
        return self._extract_data(response)
    except Exception as e:
        logger.error(f"Error getting patients: {e}")
        raise

async def admit_patient(self, patient_data: Dict[str, Any]) -> Dict[str, Any]:
    """Admit a new patient"""
    try:
        response = await self._make_request("POST", "/api/Configuration/patients/admit", json=patient_data)
        return response
    except Exception as e:
        logger.error(f"Error admitting patient: {e}")
        raise
```

**2. Function Registry (`llm_service.py`):**
```python
"get_patients": {
    "name": "get_patients",
    "description": "Get all patients in the system",
    "parameters": {
        "type": "object",
        "properties": {},
        "required": []
    }
},
"admit_patient": {
    "name": "admit_patient", 
    "description": "Admit a new patient",
    "parameters": {
        "type": "object",
        "properties": {
            "patient_data": {
                "type": "object",
                "description": "Patient information including name, age, etc."
            }
        },
        "required": ["patient_data"]
    }
},
```

**3. Execution Logic (`llm_service.py`):**
```python
elif function_name == "get_patients":
    result = await self.ipu_client.get_patients()
elif function_name == "admit_patient":
    result = await self.ipu_client.admit_patient(arguments["patient_data"])
```

**4. Message Handling (`llm_service.py`):**
```python
# Handle patient queries
elif any(keyword in user_lower for keyword in ["patient", "patients"]):
    if "admit" in user_lower or "add" in user_lower:
        # Handle patient admission
        # Extract patient details and call admit_patient
    else:
        # Handle patient listing
        if self.function_registry:
            function_result = await self.function_registry.execute_function("get_patients", {})
            # Process and display results
```

---

## 🗣️ **Natural Language Patterns**

### **Common Regex Patterns:**

```python
# Extract single word/name
r'(?:command|action)\s+(\w+)'

# Extract two parameters
r'(?:command)\s+(\w+)\s+to\s+(\w+)'

# Extract with optional parameters
r'(?:command)\s+(\w+)(?:\s+with\s+(\w+))?'

# Extract from complex sentences
r'(?:please|can you)?\s*(?:command)\s+(?:the\s+)?(\w+)'

# Extract multiple words
r'(?:command)\s+([a-zA-Z0-9_\-\s]+?)(?:\s|$)'
```

### **Example Natural Language Commands:**

```python
# For patient management:
"show me all patients"           → get_patients()
"admit patient John Doe"         → admit_patient({"name": "John Doe"})
"discharge patient John Doe"     → discharge_patient("John Doe")

# For room assignments:
"assign room 101 to patient John" → assign_room("101", "John")
"move patient John to room 102"   → move_patient("John", "102")

# For reports:
"generate occupancy report"       → generate_report("occupancy")
"show statistics for building A"  → get_building_stats("A")
```

---

## 🧪 **Testing**

### **Create Test File (`test_your_feature.py`):**

```python
#!/usr/bin/env python3
"""
Test your new feature
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_your_feature():
    """Test your new feature"""
    print("🧪 Testing Your New Feature")
    print("=" * 60)
    
    try:
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Test direct API call
        print("1️⃣ Testing direct API call:")
        try:
            result = await client.your_new_function("test_param")
            print(f"✅ Direct API: {result}")
        except Exception as e:
            print(f"❌ Direct API error: {e}")
        
        # Test chatbot integration
        test_queries = [
            "your test command here",
            "another test command"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{i+1}️⃣ Testing: '{query}'")
            try:
                result = await asyncio.wait_for(
                    chatbot.process_message('test-session', query),
                    timeout=20.0
                )
                
                print(f"Result type: {result.get('type')}")
                print(f"Response: {result.get('content', '')[:100]}...")
                
            except Exception as e:
                print(f"❌ Error: {e}")
        
        print(f"\n✅ Test completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == '__main__':
    asyncio.run(test_your_feature())
```

---

## 📝 **Quick Reference Checklist**

When adding new functionality:

- [ ] **Step 1:** Add API client method in `ipu_client.py`
- [ ] **Step 2:** Register function in `llm_service.py` function registry
- [ ] **Step 3:** Add execution logic in `llm_service.py`
- [ ] **Step 4:** Add message handling patterns in `llm_service.py`
- [ ] **Step 5:** Create test file
- [ ] **Step 6:** Test direct API calls
- [ ] **Step 7:** Test chatbot integration
- [ ] **Step 8:** Test natural language commands

---

## 🎯 **Tips for Success**

1. **Start Simple:** Begin with basic GET operations before complex POST/PUT/DELETE
2. **Test Early:** Test each step before moving to the next
3. **Use Existing Patterns:** Copy similar existing functionality as a template
4. **Handle Errors:** Always include proper error handling
5. **Document:** Add clear descriptions and comments
6. **Natural Language:** Think about how users would naturally ask for this functionality

---

**🎉 Happy Coding!** This guide should help you add any new API functionality to your IPU MCP Chatbot system.
