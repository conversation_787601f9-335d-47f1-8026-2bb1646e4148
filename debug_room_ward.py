#!/usr/bin/env python3
"""
Debug room by ward condition
"""

def test_conditions():
    """Test the conditions"""
    test_message = "rooms in Surgical Ward"
    user_lower = test_message.lower()
    
    print(f"Testing message: '{test_message}'")
    print(f"user_lower: '{user_lower}'")
    print()
    
    # Test main condition
    room_keywords = any(keyword in user_lower for keyword in ["room", "rooms", "bed", "beds"])
    print(f"Room keywords found: {room_keywords}")
    
    # Test room by ward condition
    condition1 = "ward" in user_lower and "room" in user_lower
    print(f"Condition 1 ('ward' in user_lower and 'room' in user_lower): {condition1}")
    
    condition2_part1 = "in" in user_lower
    condition2_part2 = "room" in user_lower
    condition2_part3 = any(word in user_lower for word in ["surgical", "emergency", "ward"])
    condition2 = condition2_part1 and condition2_part2 and condition2_part3
    print(f"Condition 2 ('in' in user_lower and 'room' in user_lower and surgical/emergency/ward): {condition2}")
    print(f"  - 'in' in user_lower: {condition2_part1}")
    print(f"  - 'room' in user_lower: {condition2_part2}")
    print(f"  - surgical/emergency/ward found: {condition2_part3}")
    
    overall_condition = condition1 or condition2
    print(f"Overall room by ward condition: {overall_condition}")
    
    # Test regex patterns
    import re
    patterns = [
        r'room.*(?:in|of|for)\s+(?:ward\s+)?([a-zA-Z0-9_\-\s]+?)(?:\s*$|\s+(?:and|or|,))',
        r'(?:in|of|for)\s+ward\s+([a-zA-Z0-9_\-\s]+?)(?:\s*$|\s+(?:and|or|,))',
        r'(?:in|of)\s+([a-zA-Z0-9_\-\s]+?\s+ward)(?:\s*$|\s+(?:and|or|,))',
        r'(?:in|of)\s+([a-zA-Z0-9_\-\s]+?)(?:\s*$|\s+(?:and|or|,))'
    ]
    
    print(f"\nTesting regex patterns:")
    for i, pattern in enumerate(patterns, 1):
        match = re.search(pattern, test_message, re.IGNORECASE)
        if match:
            print(f"Pattern {i}: MATCH - '{match.group(1).strip()}'")
        else:
            print(f"Pattern {i}: NO MATCH")

if __name__ == '__main__':
    test_conditions()
