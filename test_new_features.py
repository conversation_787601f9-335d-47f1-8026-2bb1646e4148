#!/usr/bin/env python3
"""
Test the new features: wards by building, rooms by ward, available rooms
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_new_features():
    """Test the new features"""
    print("🧪 Testing New Features")
    print("=" * 60)
    
    try:
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Test 1: Wards by building
        print("1️⃣ Testing Wards by Building:")
        print("-" * 40)
        
        try:
            result = await asyncio.wait_for(
                chatbot.process_message('test-session', 'please give me list of all wards of building demo785'),
                timeout=30.0
            )
            
            print(f"Result type: {result.get('type')}")
            content = result.get('content', '')
            
            if result.get('type') == 'success':
                print("✅ Wards by building query successful!")
            else:
                print("❌ Wards by building query failed")
            
            print(f"Response preview: {content[:200]}...")
            
        except asyncio.TimeoutError:
            print("⏱️ Wards by building timeout")
        except Exception as e:
            print(f"❌ Wards by building error: {e}")
        
        # Test 2: Rooms by ward
        print(f"\n2️⃣ Testing Rooms by Ward:")
        print("-" * 40)
        
        try:
            result = await asyncio.wait_for(
                chatbot.process_message('test-session', 'show me rooms in surgical ward'),
                timeout=30.0
            )
            
            print(f"Result type: {result.get('type')}")
            content = result.get('content', '')
            
            if result.get('type') == 'success':
                print("✅ Rooms by ward query successful!")
            else:
                print("❌ Rooms by ward query failed")
            
            print(f"Response preview: {content[:200]}...")
            
        except asyncio.TimeoutError:
            print("⏱️ Rooms by ward timeout")
        except Exception as e:
            print(f"❌ Rooms by ward error: {e}")
        
        # Test 3: Available rooms
        print(f"\n3️⃣ Testing Available Rooms:")
        print("-" * 40)
        
        try:
            result = await asyncio.wait_for(
                chatbot.process_message('test-session', 'show me available rooms'),
                timeout=30.0
            )
            
            print(f"Result type: {result.get('type')}")
            content = result.get('content', '')
            
            if result.get('type') == 'success':
                print("✅ Available rooms query successful!")
            else:
                print("❌ Available rooms query failed")
            
            print(f"Response preview: {content[:200]}...")
            
        except asyncio.TimeoutError:
            print("⏱️ Available rooms timeout")
        except Exception as e:
            print(f"❌ Available rooms error: {e}")
        
        # Test 4: Direct API calls
        print(f"\n4️⃣ Testing Direct API Calls:")
        print("-" * 40)
        
        try:
            # Test get_wards_by_building_name
            wards = await client.get_wards_by_building_name("demo785")
            print(f"✅ Direct API: Found {len(wards)} wards in demo785")
            
            # Test get_available_rooms
            available_rooms = await client.get_available_rooms()
            print(f"✅ Direct API: Found {len(available_rooms)} available rooms")
            
            # Test get_rooms_by_ward_name
            rooms = await client.get_rooms_by_ward_name("Surgical Ward")
            print(f"✅ Direct API: Found {len(rooms)} rooms in Surgical Ward")
            
        except Exception as e:
            print(f"❌ Direct API error: {e}")
        
        print(f"\n✅ New features test completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_new_features())
