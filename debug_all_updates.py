#!/usr/bin/env python3
"""
Debug all update functions - buildings, wards, and rooms
"""

import asyncio
import httpx
import json

async def debug_all_updates():
    """Debug all update endpoints"""
    print("🔍 Debugging All Update Endpoints")
    print("=" * 60)
    
    base_url = "http://localhost:5077"
    
    async with httpx.AsyncClient() as client:
        # First, get current data for all types
        print("1️⃣ Getting current data:")
        print("-" * 40)
        
        # Get buildings
        try:
            response = await client.get(f"{base_url}/api/Configuration/buildings")
            buildings = response.json()
            print(f"✅ Buildings found: {len(buildings.get('data', []))}")
            for building in buildings.get('data', [])[:3]:
                print(f"   - {building.get('localityTitle', 'Unknown')} (ID: {building.get('localityID')})")
        except Exception as e:
            print(f"❌ Error getting buildings: {e}")
        
        # Get wards
        try:
            response = await client.get(f"{base_url}/api/Configuration/wards")
            wards = response.json()
            print(f"✅ Wards found: {len(wards.get('data', []))}")
            for ward in wards.get('data', [])[:3]:
                print(f"   - {ward.get('localityTitle', 'Unknown')} (ID: {ward.get('localityID')})")
        except Exception as e:
            print(f"❌ Error getting wards: {e}")
        
        # Get rooms
        try:
            response = await client.get(f"{base_url}/api/Configuration/rooms")
            rooms = response.json()
            print(f"✅ Rooms found: {len(rooms.get('data', []))}")
            for room in rooms.get('data', [])[:3]:
                print(f"   - {room.get('localityTitle', 'Unknown')} (ID: {room.get('localityID')})")
        except Exception as e:
            print(f"❌ Error getting rooms: {e}")
        
        # Test update endpoints
        print(f"\n2️⃣ Testing update endpoints:")
        print("-" * 40)
        
        # Test data with isActive
        test_data = {
            "localityTitle": "TestUpdate",
            "isActive": True
        }
        
        # Test building update
        if buildings.get('data'):
            first_building = buildings['data'][0]['localityTitle']
            print(f"\n🏢 Testing building update: '{first_building}'")
            try:
                response = await client.put(
                    f"{base_url}/api/Configuration/buildings/update-by-name/{first_building}",
                    json=test_data,
                    timeout=10.0
                )
                print(f"   Status: {response.status_code}")
                if response.status_code != 200:
                    print(f"   Response: {response.text[:300]}")
                else:
                    result = response.json()
                    print(f"   ✅ Success: {result.get('data', {}).get('localityTitle')}")
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        # Test ward update
        if wards.get('data'):
            first_ward = wards['data'][0]['localityTitle']
            print(f"\n🏥 Testing ward update: '{first_ward}'")
            try:
                response = await client.put(
                    f"{base_url}/api/Configuration/wards/update-by-name/{first_ward}",
                    json=test_data,
                    timeout=10.0
                )
                print(f"   Status: {response.status_code}")
                if response.status_code != 200:
                    print(f"   Response: {response.text[:300]}")
                else:
                    result = response.json()
                    print(f"   ✅ Success: {result.get('data', {}).get('localityTitle')}")
            except Exception as e:
                print(f"   ❌ Error: {e}")
        else:
            print(f"\n🏥 No wards found to test")
        
        # Test room update
        if rooms.get('data'):
            first_room = rooms['data'][0]['localityTitle']
            print(f"\n🏠 Testing room update: '{first_room}'")
            try:
                response = await client.put(
                    f"{base_url}/api/Configuration/rooms/update-by-name/{first_room}",
                    json=test_data,
                    timeout=10.0
                )
                print(f"   Status: {response.status_code}")
                if response.status_code != 200:
                    print(f"   Response: {response.text[:300]}")
                else:
                    result = response.json()
                    print(f"   ✅ Success: {result.get('data', {}).get('localityTitle')}")
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        # Check Swagger for all update endpoints
        print(f"\n3️⃣ Checking Swagger for all update endpoints:")
        print("-" * 40)
        
        try:
            swagger_response = await client.get(f"{base_url}/swagger/v1/swagger.json")
            swagger_data = swagger_response.json()
            paths = swagger_data.get('paths', {})
            
            update_endpoints = [
                "/api/Configuration/buildings/update-by-name/{buildingName}",
                "/api/Configuration/wards/update-by-name/{wardName}",
                "/api/Configuration/rooms/update-by-name/{roomName}"
            ]
            
            for endpoint in update_endpoints:
                if endpoint in paths:
                    print(f"✅ {endpoint} - EXISTS")
                    methods = list(paths[endpoint].keys())
                    print(f"   Methods: {methods}")
                else:
                    print(f"❌ {endpoint} - NOT FOUND")
            
            # Show all available endpoints with 'update' in the name
            print(f"\nAll endpoints with 'update':")
            for path in paths.keys():
                if 'update' in path.lower():
                    methods = list(paths[path].keys())
                    print(f"   {path} ({methods})")
                    
        except Exception as e:
            print(f"❌ Error getting Swagger: {e}")
        
        # Test with different data formats
        print(f"\n4️⃣ Testing different data formats:")
        print("-" * 40)
        
        if rooms.get('data'):
            test_room = rooms['data'][0]['localityTitle']
            
            data_formats = [
                {"localityTitle": "TestRoom1", "isActive": True},
                {"localityTitle": "TestRoom2", "isActive": True, "localityCode": "TR2"},
                {"localityTitle": "TestRoom3", "isActive": True, "localityDescription": "Test room"},
                {"isActive": True},  # Only required field
            ]
            
            for i, data in enumerate(data_formats, 1):
                print(f"\n   Format {i}: {json.dumps(data)}")
                try:
                    response = await client.put(
                        f"{base_url}/api/Configuration/rooms/update-by-name/{test_room}",
                        json=data,
                        timeout=5.0
                    )
                    print(f"   Status: {response.status_code}")
                    if response.status_code == 200:
                        print(f"   ✅ This format works!")
                        break
                    else:
                        print(f"   Response: {response.text[:100]}")
                except Exception as e:
                    print(f"   Error: {str(e)[:100]}")

if __name__ == '__main__':
    asyncio.run(debug_all_updates())
