# Core MCP and HTTP dependencies
mcp>=1.0.0
httpx>=0.25.0
pydantic>=2.0.0

# Async and JSON handling
aiofiles>=23.0.0
asyncio-mqtt>=0.11.0

# CLI and interactive features
click>=8.0.0
rich>=13.0.0
prompt-toolkit>=3.0.0

# Logging and configuration
python-dotenv>=1.0.0
structlog>=23.0.0

# Testing dependencies
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-httpx>=0.21.0

# Web interface dependencies
flask>=2.3.0
flask-socketio>=5.3.0
flask-cors>=4.0.0

# LLM and AI dependencies
openai>=1.3.0
anthropic>=0.7.0
tiktoken>=0.5.0
langchain>=0.1.0
langchain-openai>=0.0.5
langchain-anthropic>=0.1.0

# Hugging Face dependencies
transformers>=4.30.0
torch>=2.0.0
tokenizers>=0.13.0
accelerate>=0.20.0

# Development tools
black>=23.0.0
flake8>=6.0.0
mypy>=1.0.0
