#!/usr/bin/env python3
"""
Setup script for IPU MCP Chatbot

This script helps set up the IPU MCP Chatbot environment.
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path


def print_header(title):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")


def print_step(step, description):
    """Print a step description"""
    print(f"\n{step}. {description}")


def run_command(command, description=""):
    """Run a shell command and return success status"""
    try:
        print(f"   Running: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ Success")
            return True
        else:
            print(f"   ❌ Failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


def check_python_version():
    """Check Python version"""
    print_step(1, "Checking Python version")
    
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"   ✅ Python {version.major}.{version.minor}.{version.micro} (OK)")
        return True
    else:
        print(f"   ❌ Python {version.major}.{version.minor}.{version.micro} (Requires 3.8+)")
        return False


def install_dependencies():
    """Install Python dependencies"""
    print_step(2, "Installing Python dependencies")
    
    if not os.path.exists("requirements.txt"):
        print("   ❌ requirements.txt not found")
        return False
    
    return run_command(f"{sys.executable} -m pip install -r requirements.txt")


def create_env_file():
    """Create .env file from example"""
    print_step(3, "Creating environment configuration")
    
    if os.path.exists(".env"):
        print("   ℹ️ .env file already exists, skipping")
        return True
    
    if os.path.exists(".env.example"):
        try:
            with open(".env.example", "r") as src, open(".env", "w") as dst:
                dst.write(src.read())
            print("   ✅ Created .env from .env.example")
            return True
        except Exception as e:
            print(f"   ❌ Failed to create .env: {e}")
            return False
    else:
        print("   ⚠️ .env.example not found, creating basic .env")
        try:
            with open(".env", "w") as f:
                f.write("# IPU MCP Chatbot Configuration\n")
                f.write("IPU_API_URL=http://localhost:5077\n")
                f.write("LOG_LEVEL=INFO\n")
            print("   ✅ Created basic .env file")
            return True
        except Exception as e:
            print(f"   ❌ Failed to create .env: {e}")
            return False


async def test_api_connection():
    """Test connection to IPU API"""
    print_step(4, "Testing IPU API connection")
    
    try:
        # Import here to avoid issues if dependencies aren't installed yet
        from ipu_client import create_client
        
        client = create_client()
        health_ok = await client.health_check()
        await client.close()
        
        if health_ok:
            print("   ✅ IPU API connection successful")
            return True
        else:
            print("   ⚠️ IPU API connection failed - check if API is running")
            return False
    except ImportError as e:
        print(f"   ⚠️ Cannot test API connection - missing dependencies: {e}")
        return False
    except Exception as e:
        print(f"   ⚠️ API connection test failed: {e}")
        return False


def create_directories():
    """Create necessary directories"""
    print_step(5, "Creating directories")
    
    directories = ["logs", "docs"]
    success = True
    
    for directory in directories:
        try:
            Path(directory).mkdir(exist_ok=True)
            print(f"   ✅ Created/verified directory: {directory}")
        except Exception as e:
            print(f"   ❌ Failed to create directory {directory}: {e}")
            success = False
    
    return success


def print_next_steps():
    """Print next steps for the user"""
    print_header("Setup Complete! 🎉")
    
    print("""
Next Steps:

1. Start the IPU Management System API (if not already running):
   - Ensure it's running on http://localhost:5077

2. Test the setup:
   python test_mcp.py

3. Try the interactive chatbot:
   python chatbot.py

4. Or start the MCP server for LLM integration:
   python mcp_server.py

5. For Claude Desktop integration, see:
   docs/claude-integration.md

Configuration:
- Edit .env file to customize settings
- Check docs/quick-start.md for more information

Troubleshooting:
- Run 'python test_mcp.py' to diagnose issues
- Check logs for detailed error information
- Ensure IPU API is accessible at the configured URL
""")


async def main():
    """Main setup function"""
    print_header("IPU MCP Chatbot Setup")
    
    print("This script will help you set up the IPU MCP Chatbot environment.")
    
    # Check Python version
    if not check_python_version():
        print("\n❌ Setup failed: Python 3.8+ is required")
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed: Could not install dependencies")
        sys.exit(1)
    
    # Create environment file
    if not create_env_file():
        print("\n⚠️ Warning: Could not create .env file")
    
    # Create directories
    if not create_directories():
        print("\n⚠️ Warning: Could not create all directories")
    
    # Test API connection
    await test_api_connection()
    
    # Print next steps
    print_next_steps()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\nSetup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\nSetup failed with error: {e}")
        sys.exit(1)
