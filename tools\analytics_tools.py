"""
Analytics and statistics tools for MCP
"""

from typing import Any, Dict, List
from mcp.types import Tool, TextContent
from .base_tool import BaseTool


class OccupancyStatsTools(BaseTool):
    """Tools for occupancy statistics"""
    
    def get_tool_definition(self) -> Tool:
        """Get occupancy stats tool definition"""
        return Tool(
            name="get_occupancy_stats",
            description="Get occupancy statistics for all rooms in the IPU system",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute get occupancy stats"""
        stats = await self.client.get_occupancy_stats()
        
        if not stats:
            return [TextContent(type="text", text="📊 Unable to retrieve occupancy statistics.")]
        
        # Format stats
        occupancy_rate = stats.get('occupancyRate', 0)
        status_emoji = "🔴" if occupancy_rate > 80 else "🟡" if occupancy_rate > 60 else "🟢"
        
        stats_text = f"""📊 **IPU Occupancy Statistics**

{status_emoji} **Overall Occupancy Rate:** {occupancy_rate}%

**Room Summary:**
• Total Rooms: {stats.get('totalRooms', 0)}
• Occupied Rooms: {stats.get('occupiedRooms', 0)}
• Available Rooms: {stats.get('availableRooms', 0)}

**Special Rooms:**
• Isolation Rooms: {stats.get('isolationRooms', 0)}
• Home Care Rooms: {stats.get('homeCareRooms', 0)}
"""
        
        # Add room type breakdown if available
        room_types = stats.get('roomTypeBreakdown', {})
        if room_types:
            stats_text += "\n**Room Types:**\n"
            for room_type, count in room_types.items():
                stats_text += f"• {room_type}: {count} rooms\n"
        
        return [TextContent(type="text", text=stats_text)]


class SystemStatsTools(BaseTool):
    """Tools for overall system statistics"""
    
    def get_tool_definition(self) -> Tool:
        """Get system stats tool definition"""
        return Tool(
            name="get_system_stats",
            description="Get comprehensive statistics about the entire IPU system",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute get system stats"""
        try:
            # Get all data
            buildings = await self.client.get_buildings()
            wards = await self.client.get_wards()
            rooms = await self.client.get_rooms()
            occupancy_stats = await self.client.get_occupancy_stats()
            
            # Calculate comprehensive stats
            total_buildings = len(buildings)
            active_buildings = sum(1 for b in buildings if b.get('isActive', False))
            
            total_wards = len(wards)
            active_wards = sum(1 for w in wards if w.get('isActive', False))
            
            total_rooms = len(rooms)
            active_rooms = sum(1 for r in rooms if r.get('isActive', False))
            occupied_rooms = sum(1 for r in rooms if r.get('isOccupied', False))
            
            # Room type analysis
            room_types = {}
            for room in rooms:
                room_type = room.get('type', 'Unknown')
                room_types[room_type] = room_types.get(room_type, 0) + 1
            
            # Building utilization
            building_utilization = []
            for building in buildings:
                try:
                    building_wards = await self.client.get_wards_by_building(building['id'])
                    building_rooms = []
                    for ward in building_wards:
                        ward_rooms = await self.client.get_rooms_by_ward(ward['id'])
                        building_rooms.extend(ward_rooms)
                    
                    total_building_rooms = len(building_rooms)
                    occupied_building_rooms = sum(1 for r in building_rooms if r.get('isOccupied', False))
                    utilization = (occupied_building_rooms / total_building_rooms * 100) if total_building_rooms > 0 else 0
                    
                    building_utilization.append({
                        'name': building.get('title', 'Unknown'),
                        'rooms': total_building_rooms,
                        'occupied': occupied_building_rooms,
                        'utilization': round(utilization, 1)
                    })
                except Exception:
                    building_utilization.append({
                        'name': building.get('title', 'Unknown'),
                        'rooms': 0,
                        'occupied': 0,
                        'utilization': 0
                    })
            
            # Format comprehensive stats
            occupancy_rate = occupancy_stats.get('occupancyRate', 0)
            status_emoji = "🔴" if occupancy_rate > 80 else "🟡" if occupancy_rate > 60 else "🟢"
            
            stats_text = f"""📊 **IPU System Overview**

{status_emoji} **System Status:** {occupancy_rate}% Occupancy

**Infrastructure:**
🏢 Buildings: {total_buildings} total ({active_buildings} active)
🏥 Wards: {total_wards} total ({active_wards} active)
🛏️ Rooms: {total_rooms} total ({active_rooms} active)

**Occupancy:**
• Occupied: {occupied_rooms} rooms
• Available: {total_rooms - occupied_rooms} rooms
• Utilization Rate: {occupancy_rate}%

**Room Types:**
"""
            
            for room_type, count in sorted(room_types.items()):
                percentage = (count / total_rooms * 100) if total_rooms > 0 else 0
                stats_text += f"• {room_type}: {count} rooms ({percentage:.1f}%)\n"
            
            stats_text += "\n**Building Utilization:**\n"
            for building in sorted(building_utilization, key=lambda x: x['utilization'], reverse=True):
                stats_text += f"• {building['name']}: {building['utilization']}% ({building['occupied']}/{building['rooms']})\n"
            
            return [TextContent(type="text", text=stats_text)]
            
        except Exception as e:
            return self.create_error_response(f"Failed to generate system stats: {str(e)}")


class CapacityAnalysisTools(BaseTool):
    """Tools for capacity analysis"""
    
    def get_tool_definition(self) -> Tool:
        """Get capacity analysis tool definition"""
        return Tool(
            name="analyze_capacity",
            description="Analyze room capacity and availability by type and location",
            inputSchema={
                "type": "object",
                "properties": {
                    "room_type": {
                        "type": "string",
                        "description": "Optional: Filter by room type (e.g., 'Isolation', 'Cardiac')"
                    }
                },
                "required": []
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute capacity analysis"""
        try:
            rooms = await self.client.get_rooms()
            
            if not rooms:
                return [TextContent(type="text", text="📊 No rooms found for capacity analysis.")]
            
            # Filter by room type if specified
            room_type_filter = arguments.get('room_type')
            if room_type_filter:
                rooms = [r for r in rooms if r.get('type', '').lower() == room_type_filter.lower()]
                if not rooms:
                    return [TextContent(type="text", text=f"📊 No rooms found of type '{room_type_filter}'.")]
            
            # Analyze capacity by ward
            ward_capacity = {}
            for room in rooms:
                ward_title = room.get('wardTitle', 'Unknown Ward')
                if ward_title not in ward_capacity:
                    ward_capacity[ward_title] = {
                        'total': 0,
                        'occupied': 0,
                        'available': 0,
                        'types': {}
                    }
                
                ward_capacity[ward_title]['total'] += 1
                if room.get('isOccupied', False):
                    ward_capacity[ward_title]['occupied'] += 1
                else:
                    ward_capacity[ward_title]['available'] += 1
                
                room_type = room.get('type', 'Unknown')
                ward_capacity[ward_title]['types'][room_type] = ward_capacity[ward_title]['types'].get(room_type, 0) + 1
            
            # Format analysis
            filter_text = f" ({room_type_filter} rooms)" if room_type_filter else ""
            analysis_text = f"""📊 **Capacity Analysis{filter_text}**

**Ward-by-Ward Breakdown:**
"""
            
            for ward, data in sorted(ward_capacity.items()):
                utilization = (data['occupied'] / data['total'] * 100) if data['total'] > 0 else 0
                status_emoji = "🔴" if utilization > 80 else "🟡" if utilization > 60 else "🟢"
                
                analysis_text += f"\n{status_emoji} **{ward}**\n"
                analysis_text += f"• Total: {data['total']} rooms\n"
                analysis_text += f"• Occupied: {data['occupied']} rooms\n"
                analysis_text += f"• Available: {data['available']} rooms\n"
                analysis_text += f"• Utilization: {utilization:.1f}%\n"
                
                if len(data['types']) > 1:
                    analysis_text += "• Room Types: " + ", ".join([f"{t}({c})" for t, c in data['types'].items()]) + "\n"
            
            # Overall summary
            total_rooms = sum(data['total'] for data in ward_capacity.values())
            total_occupied = sum(data['occupied'] for data in ward_capacity.values())
            total_available = sum(data['available'] for data in ward_capacity.values())
            overall_utilization = (total_occupied / total_rooms * 100) if total_rooms > 0 else 0
            
            analysis_text += f"\n**Overall Summary{filter_text}:**\n"
            analysis_text += f"• Total Rooms: {total_rooms}\n"
            analysis_text += f"• Occupied: {total_occupied}\n"
            analysis_text += f"• Available: {total_available}\n"
            analysis_text += f"• System Utilization: {overall_utilization:.1f}%\n"
            
            return [TextContent(type="text", text=analysis_text)]
            
        except Exception as e:
            return self.create_error_response(f"Failed to analyze capacity: {str(e)}")


class TrendAnalysisTools(BaseTool):
    """Tools for trend analysis (simulated)"""
    
    def get_tool_definition(self) -> Tool:
        """Get trend analysis tool definition"""
        return Tool(
            name="analyze_trends",
            description="Analyze occupancy trends and patterns (simulated data for demo)",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute trend analysis"""
        try:
            # Get current stats
            stats = await self.client.get_occupancy_stats()
            current_occupancy = stats.get('occupancyRate', 0)
            
            # Simulate trend data (in a real system, this would come from historical data)
            import random
            
            # Generate simulated weekly trend
            weekly_trend = []
            base_occupancy = current_occupancy
            for day in ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']:
                # Simulate realistic hospital occupancy patterns
                if day in ['Sat', 'Sun']:
                    occupancy = base_occupancy * random.uniform(0.8, 0.95)  # Lower on weekends
                else:
                    occupancy = base_occupancy * random.uniform(0.95, 1.1)  # Higher on weekdays
                weekly_trend.append((day, min(100, max(0, occupancy))))
            
            # Generate insights
            avg_occupancy = sum(occ for _, occ in weekly_trend) / len(weekly_trend)
            peak_day = max(weekly_trend, key=lambda x: x[1])
            low_day = min(weekly_trend, key=lambda x: x[1])
            
            trend_text = f"""📈 **Occupancy Trend Analysis**

**Current Status:** {current_occupancy:.1f}% occupancy

**Weekly Trend (Simulated):**
"""
            
            for day, occupancy in weekly_trend:
                trend_emoji = "📈" if occupancy > avg_occupancy else "📉"
                trend_text += f"{trend_emoji} {day}: {occupancy:.1f}%\n"
            
            trend_text += f"""
**Insights:**
• Average Weekly Occupancy: {avg_occupancy:.1f}%
• Peak Day: {peak_day[0]} ({peak_day[1]:.1f}%)
• Lowest Day: {low_day[0]} ({low_day[1]:.1f}%)
• Weekly Variation: {peak_day[1] - low_day[1]:.1f} percentage points

**Recommendations:**
"""
            
            if current_occupancy > 85:
                trend_text += "🔴 High occupancy - consider capacity planning\n"
            elif current_occupancy > 70:
                trend_text += "🟡 Moderate occupancy - monitor closely\n"
            else:
                trend_text += "🟢 Good capacity available\n"
            
            if peak_day[1] - low_day[1] > 20:
                trend_text += "📊 High variation - consider staffing adjustments\n"
            
            trend_text += "\n*Note: Trend data is simulated for demonstration purposes*"
            
            return [TextContent(type="text", text=trend_text)]
            
        except Exception as e:
            return self.create_error_response(f"Failed to analyze trends: {str(e)}")
