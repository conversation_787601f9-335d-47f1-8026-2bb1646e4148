#!/usr/bin/env python3
"""
Test room name parsing specifically
"""

def test_room_parsing():
    """Test room name parsing logic"""
    print("🧪 Testing Room Name Parsing Logic")
    print("=" * 50)
    
    test_cases = [
        "update room name Room 1 to TestRoom",
        "update room name Building One to NewRoom",
        "change room name surgicalroom to OperatingRoom",
        "modify room name Room 2 to PatientRoom2"
    ]
    
    for i, message in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ Testing: '{message}'")
        
        # Simulate the parsing logic
        if " to " in message:
            parts = message.split(" to ")
            old_name_part = parts[0]
            new_name = parts[1].strip()
            
            print(f"   Old name part: '{old_name_part}'")
            print(f"   New name: '{new_name}'")
            
            # Extract old room name - improved parsing for multi-word names
            old_name = None
            words = old_name_part.split()

            print(f"   Words: {words}")

            # Look for the pattern "room name" and extract everything after it
            room_name_found = False
            for i in range(len(words) - 1):
                if words[i].lower() == "room" and words[i + 1].lower() == "name":
                    # Found "room name", extract everything after it
                    if i + 2 < len(words):
                        name_words = words[i + 2:]
                        old_name = " ".join(name_words)
                        room_name_found = True
                        print(f"   Found 'room name' pattern at position {i}")
                        print(f"   Name words: {name_words}")
                        break

            # If "room name" pattern not found, use fallback logic
            if not room_name_found:
                print(f"   'room name' pattern not found, using fallback")
                # Find the room name - collect all words after the last command word
                command_words = ["update", "room", "change", "modify", "edit", "rename"]
                name_words = []

                # Find the last command word position
                last_command_pos = -1
                for j, word in enumerate(words):
                    if word.lower() in command_words:
                        last_command_pos = j

                print(f"   Last command position: {last_command_pos}")

                # Collect all words after the last command word
                if last_command_pos >= 0 and last_command_pos + 1 < len(words):
                    name_words = words[last_command_pos + 1:]
                    old_name = " ".join(name_words)
                elif len(words) > 0:
                    # Fallback: use the last word
                    old_name = words[-1]

                print(f"   Name words: {name_words}")

            print(f"   Extracted old name: '{old_name}'")
            
            if old_name == "Room 1":
                print(f"   ✅ Correctly extracted 'Room 1'")
            elif old_name == "Building One":
                print(f"   ✅ Correctly extracted 'Building One'")
            elif old_name == "surgicalroom":
                print(f"   ✅ Correctly extracted 'surgicalroom'")
            elif old_name == "Room 2":
                print(f"   ✅ Correctly extracted 'Room 2'")
            else:
                print(f"   ❌ Incorrect extraction")

if __name__ == '__main__':
    test_room_parsing()
