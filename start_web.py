#!/usr/bin/env python3
"""
IPU MCP Chatbot Web Interface Launcher

Simple launcher script for the web interface with better error handling.
"""

import logging
import sys
import webbrowser
import time
from threading import Timer

# Flask imports removed - using integrated LLM app

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def open_browser():
    """Open browser after a short delay"""
    time.sleep(2)  # Wait for server to start
    webbrowser.open('http://localhost:5000')


def main():
    """Main launcher function"""
    try:
        print("🏥 IPU MCP Chatbot Web Interface")
        print("=" * 50)
        print("Starting web server...")
        
        # Import the new integrated LLM app
        from indici_llm_app import run_app
        
        # Open browser automatically
        Timer(1.0, open_browser).start()

        print("\n🚀 Starting Indici Assistant with Professional LLM...")
        print("📱 Web Interface: http://localhost:5000")
        print("🔗 API Health Check: http://localhost:5000/api/health")
        print("\n🤖 LLM Provider Priority:")
        print("   1. Hugging Face (if USE_HUGGINGFACE=true)")
        print("   2. OpenAI (if OPENAI_API_KEY set)")
        print("   3. Anthropic (if ANTHROPIC_API_KEY set)")
        print("   4. Mock LLM (fallback)")
        print("\n💡 Sample Questions:")
        print("   • 'Show me current inpatients'")
        print("   • 'What is the bed occupancy rate?'")
        print("   • 'Find available isolation rooms'")
        print("   • 'Process patient discharge'")
        print("\n⚠️ Note: Full functionality requires IPU Management System API")
        print("   Expected at: http://localhost:5077")
        print("\n🛑 Press Ctrl+C to stop the server")
        print("=" * 50)

        # Start the integrated LLM application
        run_app()
        
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Failed to start web application: {e}")
        print("\nTroubleshooting:")
        print("1. Check if port 5000 is available")
        print("2. Ensure all dependencies are installed: pip install -r requirements.txt")
        print("3. Check the logs above for specific errors")
        sys.exit(1)


if __name__ == '__main__':
    main()
