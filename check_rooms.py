#!/usr/bin/env python3
"""
Check available rooms
"""

import asyncio
from ipu_client import create_client

async def check_rooms():
    """Check available rooms"""
    print("🏥 Checking Available Rooms")
    print("=" * 40)
    
    try:
        client = create_client()
        rooms = await client.get_rooms()
        
        print(f"Found {len(rooms)} rooms:")
        for i, room in enumerate(rooms[:10]):  # Show first 10
            name = room.get('localityTitle', 'Unknown')
            room_id = room.get('localityID', 'N/A')
            print(f"  {i+1}. '{name}' (ID: {room_id})")
        
        # Check if @@#$ exists
        target_room = None
        for room in rooms:
            if room.get('localityTitle') == '@@#$':
                target_room = room
                break
        
        if target_room:
            print(f"\n✅ Found target room '@@#$':")
            print(f"   ID: {target_room.get('localityID')}")
            print(f"   Code: {target_room.get('localityCode')}")
            print(f"   Ward: {target_room.get('parentLocalityTitle')}")
        else:
            print(f"\n❌ Room '@@#$' not found in the system")
            print("Available room names:")
            for room in rooms[:5]:
                print(f"   - '{room.get('localityTitle', 'Unknown')}'")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == '__main__':
    asyncio.run(check_rooms())
