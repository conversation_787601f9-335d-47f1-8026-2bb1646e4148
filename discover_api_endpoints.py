#!/usr/bin/env python3
"""
Discover all available endpoints in the IPU API
"""

import asyncio
import httpx
import json

async def discover_api_endpoints():
    """Discover all available API endpoints"""
    print("🔍 Discovering IPU API Endpoints")
    print("=" * 60)
    
    base_url = "http://localhost:5077"
    
    # Common API endpoint patterns to test
    endpoints_to_test = [
        # Health and info
        "/health",
        "/api",
        "/swagger",
        "/swagger/v1/swagger.json",
        
        # Configuration endpoints
        "/api/configuration",
        "/api/configuration/buildings",
        "/api/configuration/wards", 
        "/api/configuration/rooms",
        "/api/configuration/localities",
        
        # CRUD operations for buildings
        "/api/configuration/buildings/1",
        "/api/buildings",
        "/api/buildings/1",
        
        # CRUD operations for rooms
        "/api/configuration/rooms/1",
        "/api/rooms",
        "/api/rooms/1",
        
        # Search endpoints
        "/api/configuration/buildings/search",
        "/api/configuration/rooms/search",
        "/api/configuration/search",
        
        # Statistics endpoints
        "/api/statistics",
        "/api/analytics",
        "/api/occupancy",
        "/api/configuration/statistics",
        
        # Patient management (if available)
        "/api/patients",
        "/api/admissions",
        "/api/discharges",
        
        # Other common patterns
        "/api/localities",
        "/api/locality-types",
        "/api/reports"
    ]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        print(f"\n🌐 Testing {len(endpoints_to_test)} potential endpoints...")
        print("-" * 60)
        
        working_endpoints = []
        
        for endpoint in endpoints_to_test:
            url = f"{base_url}{endpoint}"
            
            try:
                # Test GET method
                response = await client.get(url)
                status = response.status_code
                
                if status == 200:
                    print(f"✅ GET  {endpoint} -> {status}")
                    try:
                        data = response.json()
                        if isinstance(data, dict) and 'data' in data:
                            print(f"   📊 Data items: {len(data.get('data', []))}")
                        elif isinstance(data, list):
                            print(f"   📊 Items: {len(data)}")
                        else:
                            print(f"   📄 Response type: {type(data).__name__}")
                    except:
                        print(f"   📄 Response: {response.text[:100]}...")
                    working_endpoints.append((endpoint, 'GET', status))
                    
                elif status in [405, 400, 401, 403]:  # Method not allowed, bad request, etc.
                    print(f"🔶 GET  {endpoint} -> {status} (endpoint exists)")
                    working_endpoints.append((endpoint, 'GET', status))
                    
                    # Test other HTTP methods for existing endpoints
                    for method in ['POST', 'PUT', 'DELETE']:
                        try:
                            if method == 'POST':
                                test_response = await client.post(url, json={"test": "data"})
                            elif method == 'PUT':
                                test_response = await client.put(url, json={"test": "data"})
                            elif method == 'DELETE':
                                test_response = await client.delete(url)
                            
                            if test_response.status_code in [200, 201, 204, 400, 401, 403, 405, 422]:
                                print(f"🔶 {method} {endpoint} -> {test_response.status_code}")
                                working_endpoints.append((endpoint, method, test_response.status_code))
                        except:
                            pass
                            
                elif status == 404:
                    pass  # Endpoint doesn't exist
                else:
                    print(f"⚠️  GET  {endpoint} -> {status}")
                    
            except Exception as e:
                if "timeout" not in str(e).lower():
                    print(f"❌ GET  {endpoint} -> Error: {str(e)[:50]}")
        
        print(f"\n📋 Summary: Found {len(working_endpoints)} working endpoints")
        print("=" * 60)
        
        # Group by endpoint
        endpoint_methods = {}
        for endpoint, method, status in working_endpoints:
            if endpoint not in endpoint_methods:
                endpoint_methods[endpoint] = []
            endpoint_methods[endpoint].append((method, status))
        
        for endpoint, methods in sorted(endpoint_methods.items()):
            print(f"\n🔗 {endpoint}")
            for method, status in methods:
                print(f"   {method}: {status}")

if __name__ == '__main__':
    asyncio.run(discover_api_endpoints())
