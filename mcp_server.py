#!/usr/bin/env python3
"""
IPU Management System MCP Server

This server provides LLM chatbots with access to the IPU Management System API
through the Model Context Protocol (MCP).
"""

import asyncio
import logging
import sys
from typing import Any, Dict, List
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import Tool, TextContent

# Import our modules
from config import config, validate_config
from ipu_client import create_client, IPUAPIError
from tools.base_tool import ToolRegistry
from tools.building_tools import (
    BuildingTools, BuildingDetailsTools, BuildingSearchTools, BuildingStatsTools,
    UpdateBuildingTools, CreateBuildingTools, UpdateBuildingByNameTools, DeleteBuildingTools
)
from tools.ward_tools import (
    WardTools, WardsByBuildingTools, WardDetailsTools, WardSearchTools, WardStatsTools, UpdateWardByNameTools
)
from tools.room_tools import (
    RoomTools, RoomsByWardTools, AvailableRoomsTools, IsolationRoomsTools,
    HomeCareRoomsTools, RoomSearchTools, RoomDetailsTools, UpdateRoomTools, CreateRoomTools, UpdateRoomByNameTools
)
from tools.analytics_tools import OccupancyStatsTools, SystemStatsTools, CapacityAnalysisTools, TrendAnalysisTools

# Configure logging
logging.basicConfig(
    level=getattr(logging, config.logging.level.upper()),
    format=config.logging.format,
    filename=config.logging.file
)
logger = logging.getLogger(__name__)

# Global variables
tool_registry = ToolRegistry()
ipu_client = None


async def setup_tools():
    """Setup and register all MCP tools"""
    global ipu_client, tool_registry
    
    # Create IPU client
    ipu_client = create_client()
    
    # Test connection
    try:
        health_ok = await ipu_client.health_check()
        if not health_ok:
            logger.warning("IPU API health check failed - some tools may not work properly")
    except Exception as e:
        logger.error(f"Failed to connect to IPU API: {e}")
        logger.warning("Continuing without API connection - tools will return errors")
    
    # Register building tools
    tool_registry.register(BuildingTools(ipu_client))
    tool_registry.register(BuildingDetailsTools(ipu_client))
    tool_registry.register(BuildingSearchTools(ipu_client))
    tool_registry.register(BuildingStatsTools(ipu_client))
    tool_registry.register(UpdateBuildingTools(ipu_client))
    tool_registry.register(CreateBuildingTools(ipu_client))
    tool_registry.register(UpdateBuildingByNameTools(ipu_client))
    tool_registry.register(DeleteBuildingTools(ipu_client))
    
    # Register ward tools
    tool_registry.register(WardTools(ipu_client))
    tool_registry.register(WardsByBuildingTools(ipu_client))
    tool_registry.register(WardDetailsTools(ipu_client))
    tool_registry.register(WardSearchTools(ipu_client))
    tool_registry.register(WardStatsTools(ipu_client))
    tool_registry.register(UpdateWardByNameTools(ipu_client))
    
    # Register room tools
    tool_registry.register(RoomTools(ipu_client))
    tool_registry.register(RoomsByWardTools(ipu_client))
    tool_registry.register(AvailableRoomsTools(ipu_client))
    tool_registry.register(IsolationRoomsTools(ipu_client))
    tool_registry.register(HomeCareRoomsTools(ipu_client))
    tool_registry.register(RoomSearchTools(ipu_client))
    tool_registry.register(RoomDetailsTools(ipu_client))
    tool_registry.register(UpdateRoomTools(ipu_client))
    tool_registry.register(CreateRoomTools(ipu_client))
    tool_registry.register(UpdateRoomByNameTools(ipu_client))
    
    # Register analytics tools
    tool_registry.register(OccupancyStatsTools(ipu_client))
    tool_registry.register(SystemStatsTools(ipu_client))
    tool_registry.register(CapacityAnalysisTools(ipu_client))
    tool_registry.register(TrendAnalysisTools(ipu_client))
    
    logger.info(f"Registered {len(tool_registry.tools)} MCP tools")


# Create the MCP server
server = Server(config.mcp.server_name)


@server.list_tools()
async def list_tools() -> List[Tool]:
    """List all available tools"""
    return tool_registry.get_all_tools()


@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle tool calls"""
    logger.info(f"Tool call: {name} with arguments: {arguments}")
    
    try:
        result = await tool_registry.execute_tool(name, arguments)
        logger.info(f"Tool {name} executed successfully")
        return result
    except IPUAPIError as e:
        error_msg = f"IPU API Error: {e}"
        if e.status_code:
            error_msg += f" (Status: {e.status_code})"
        logger.error(error_msg)
        return [TextContent(type="text", text=f"❌ {error_msg}")]
    except Exception as e:
        error_msg = f"Tool execution error: {str(e)}"
        logger.error(error_msg)
        return [TextContent(type="text", text=f"❌ {error_msg}")]


async def cleanup():
    """Cleanup resources"""
    global ipu_client
    if ipu_client:
        await ipu_client.close()
        logger.info("Closed IPU client connection")


async def main():
    """Main entry point"""
    logger.info(f"Starting {config.mcp.server_name} v{config.mcp.server_version}")
    logger.info(f"IPU API URL: {config.ipu.base_url}")
    
    # Validate configuration
    if not validate_config():
        logger.error("Configuration validation failed")
        sys.exit(1)
    
    # Setup tools
    try:
        await setup_tools()
    except Exception as e:
        logger.error(f"Failed to setup tools: {e}")
        sys.exit(1)
    
    # Log available tools
    tool_names = tool_registry.get_tool_names()
    logger.info(f"Available tools: {', '.join(tool_names)}")
    
    try:
        # Run the MCP server
        async with stdio_server() as (read_stream, write_stream):
            logger.info("MCP server started successfully")
            await server.run(read_stream, write_stream, server.create_initialization_options())
    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
    except Exception as e:
        logger.error(f"Server error: {e}")
        raise
    finally:
        await cleanup()
        logger.info("MCP server stopped")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nShutting down gracefully...")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
