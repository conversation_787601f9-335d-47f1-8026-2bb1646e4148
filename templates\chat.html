<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Indici Assistant - Healthcare Management</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            height: 100vh;
            display: flex;
            color: #334155;
            overflow: hidden;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 320px;
            background: #ffffff;
            border-right: 1px solid #e2e8f0;
            display: flex;
            flex-direction: column;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 10;
        }

        .sidebar-header {
            padding: 24px;
            border-bottom: 1px solid #e2e8f0;
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
        }

        .sidebar-header h1 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .sidebar-header p {
            font-size: 0.875rem;
            opacity: 0.9;
            font-weight: 400;
        }

        .sidebar-content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        .sample-queries {
            margin-bottom: 32px;
        }

        .sample-queries h3 {
            font-size: 1rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .query-category {
            margin-bottom: 24px;
        }

        .query-category h4 {
            font-size: 0.875rem;
            font-weight: 500;
            color: #64748b;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .query-item {
            padding: 12px 16px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            color: #475569;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .query-item:hover {
            background: #0ea5e9;
            color: white;
            border-color: #0ea5e9;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.15);
        }

        .query-item i {
            font-size: 1rem;
            width: 16px;
            text-align: center;
        }

        /* Main Chat Area */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #ffffff;
            height: 100vh;
        }

        .chat-header {
            background: #ffffff;
            border-bottom: 1px solid #e2e8f0;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            justify-content: between;
            min-height: 80px;
        }

        .chat-header-content {
            flex: 1;
        }

        .chat-header h1 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .chat-header p {
            font-size: 0.875rem;
            color: #64748b;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.875rem;
            color: #64748b;
            background: #f1f5f9;
            padding: 8px 12px;
            border-radius: 20px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ef4444;
            animation: pulse 2s infinite;
        }

        .status-dot.connected {
            background: #10b981;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-messages {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            background: #f8fafc;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
            max-width: 100%;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            flex-shrink: 0;
            font-weight: 500;
        }

        .message.user .message-avatar {
            background: #0ea5e9;
            color: white;
        }

        .message.bot .message-avatar {
            background: #f1f5f9;
            color: #0ea5e9;
            border: 1px solid #e2e8f0;
        }

        .message-content {
            max-width: calc(100% - 60px);
            padding: 16px 20px;
            border-radius: 12px;
            position: relative;
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: #0ea5e9;
            color: white;
        }

        .message.bot .message-content {
            background: white;
            color: #334155;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .message.bot.error .message-content {
            background: #fef2f2;
            border-color: #fecaca;
            color: #dc2626;
        }

        .message.bot.warning .message-content {
            background: #fffbeb;
            border-color: #fed7aa;
            color: #d97706;
        }

        .message.bot.success .message-content {
            background: #f0fdf4;
            border-color: #bbf7d0;
            color: #16a34a;
        }

        .message-time {
            font-size: 0.75rem;
            color: #94a3b8;
            margin-top: 6px;
            font-weight: 400;
        }

        .chat-input-container {
            padding: 20px 24px;
            background: white;
            border-top: 1px solid #e2e8f0;
        }

        .chat-input-form {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            padding: 14px 18px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            font-size: 0.875rem;
            outline: none;
            transition: all 0.2s ease;
            background: #f9fafb;
            font-family: inherit;
        }

        .chat-input:focus {
            border-color: #0ea5e9;
            background: white;
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
        }

        .send-button {
            width: 44px;
            height: 44px;
            border: none;
            border-radius: 10px;
            background: #0ea5e9;
            color: white;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send-button:hover {
            background: #0284c7;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.15);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            background: #94a3b8;
        }

        .typing-indicator {
            display: none;
            padding: 16px 20px;
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-left: 48px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .typing-text {
            font-size: 0.875rem;
            color: #64748b;
            margin-right: 8px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #0ea5e9;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); opacity: 0.4; }
            30% { transform: translateY(-4px); opacity: 1; }
        }

        .welcome-message {
            text-align: center;
            padding: 60px 40px;
            color: #64748b;
            max-width: 600px;
            margin: 0 auto;
        }

        .welcome-message h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 12px;
            color: #1e293b;
        }

        .welcome-message p {
            font-size: 1rem;
            margin-bottom: 32px;
            line-height: 1.6;
        }

        .welcome-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 32px;
        }

        .welcome-feature {
            padding: 20px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            text-align: center;
            transition: all 0.2s ease;
        }

        .welcome-feature:hover {
            border-color: #0ea5e9;
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.15);
            transform: translateY(-2px);
        }

        .welcome-feature i {
            font-size: 2rem;
            color: #0ea5e9;
            margin-bottom: 12px;
        }

        .welcome-feature h4 {
            font-size: 1rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .welcome-feature p {
            font-size: 0.875rem;
            color: #64748b;
            margin: 0;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            body {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                max-height: 40vh;
                border-right: none;
                border-bottom: 1px solid #e2e8f0;
            }

            .sidebar-content {
                padding: 16px;
            }

            .chat-container {
                height: 60vh;
            }

            .message-content {
                max-width: calc(100% - 50px);
            }

            .welcome-features {
                grid-template-columns: 1fr;
            }
        }

        /* Scrollbar styling */
        .chat-messages::-webkit-scrollbar,
        .sidebar-content::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track,
        .sidebar-content::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb,
        .sidebar-content::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover,
        .sidebar-content::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Animation for new messages */
        .message {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Focus states for accessibility */
        .chat-input:focus,
        .send-button:focus,
        .query-item:focus {
            outline: none;
        }

        /* Loading state */
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h1>Indici Assistant</h1>
            <p>Inpatient Unit Management System</p>
        </div>

        <div class="sidebar-content">
            <div class="sample-queries">
                <h3><i class="fas fa-lightbulb"></i> Sample Queries</h3>

                <div class="query-category">
                    <h4>Patient Management</h4>
                    <div class="query-item" onclick="sendExample('Show me current inpatients')">
                        <i class="fas fa-user-injured"></i>
                        Show me current inpatients
                    </div>
                    <div class="query-item" onclick="sendExample('Admit new patient')">
                        <i class="fas fa-user-plus"></i>
                        Admit new patient
                    </div>
                    <div class="query-item" onclick="sendExample('Process patient discharge')">
                        <i class="fas fa-sign-out-alt"></i>
                        Process patient discharge
                    </div>
                    <div class="query-item" onclick="sendExample('Approve home leave request')">
                        <i class="fas fa-home"></i>
                        Approve home leave request
                    </div>
                </div>

                <div class="query-category">
                    <h4>Bed & Room Management</h4>
                    <div class="query-item" onclick="sendExample('Show bed availability')">
                        <i class="fas fa-bed"></i>
                        Show bed availability
                    </div>
                    <div class="query-item" onclick="sendExample('Assign bed to patient')">
                        <i class="fas fa-user-check"></i>
                        Assign bed to patient
                    </div>
                    <div class="query-item" onclick="sendExample('Find isolation beds')">
                        <i class="fas fa-shield-alt"></i>
                        Find isolation beds
                    </div>
                    <div class="query-item" onclick="sendExample('Room transfer request')">
                        <i class="fas fa-exchange-alt"></i>
                        Room transfer request
                    </div>
                </div>

                <div class="query-category">
                    <h4>Care Management</h4>
                    <div class="query-item" onclick="sendExample('Schedule patient care plan')">
                        <i class="fas fa-calendar-check"></i>
                        Schedule patient care plan
                    </div>
                    <div class="query-item" onclick="sendExample('Update treatment status')">
                        <i class="fas fa-stethoscope"></i>
                        Update treatment status
                    </div>
                    <div class="query-item" onclick="sendExample('Medication administration')">
                        <i class="fas fa-pills"></i>
                        Medication administration
                    </div>
                    <div class="query-item" onclick="sendExample('Patient progress notes')">
                        <i class="fas fa-clipboard-list"></i>
                        Patient progress notes
                    </div>
                </div>

                <div class="query-category">
                    <h4>Reports & Analytics</h4>
                    <div class="query-item" onclick="sendExample('IPU occupancy report')">
                        <i class="fas fa-chart-pie"></i>
                        IPU occupancy report
                    </div>
                    <div class="query-item" onclick="sendExample('Patient length of stay')">
                        <i class="fas fa-clock"></i>
                        Patient length of stay
                    </div>
                    <div class="query-item" onclick="sendExample('Discharge summary report')">
                        <i class="fas fa-file-medical"></i>
                        Discharge summary report
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Chat Area -->
    <div class="chat-container">
        <div class="chat-header">
            <div class="chat-header-content">
                <h1>Inpatient Unit Management Assistant</h1>
                <p>Manage patient admissions, discharges, bed assignments, care plans, and home leave</p>
            </div>
            <div class="connection-status">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">Connecting...</span>
            </div>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <h3>Welcome to Indici Assistant! 👋</h3>
                <p>Your intelligent Inpatient Unit management companion. I can help you manage patient care, admissions, discharges, and all aspects of inpatient operations.</p>

                <div class="welcome-features">
                    <div class="welcome-feature">
                        <i class="fas fa-user-injured"></i>
                        <h4>Patient Management</h4>
                        <p>Handle admissions, discharges, transfers, and home leave requests</p>
                    </div>
                    <div class="welcome-feature">
                        <i class="fas fa-bed"></i>
                        <h4>Bed & Room Management</h4>
                        <p>Monitor bed availability, assign rooms, and manage patient placement</p>
                    </div>
                    <div class="welcome-feature">
                        <i class="fas fa-stethoscope"></i>
                        <h4>Care Coordination</h4>
                        <p>Manage care plans, treatments, medications, and patient progress</p>
                    </div>
                    <div class="welcome-feature">
                        <i class="fas fa-chart-line"></i>
                        <h4>IPU Analytics</h4>
                        <p>Track occupancy, length of stay, and operational performance</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-text">Indici Assistant is thinking</div>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>

        <div class="chat-input-container">
            <form class="chat-input-form" id="chatForm">
                <input type="text" class="chat-input" id="messageInput"
                       placeholder="Ask me about patient admissions, discharges, bed management, or care plans..."
                       autocomplete="off">
                <button type="submit" class="send-button" id="sendButton">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </form>
        </div>
    </div>

    <script>
        // Initialize Socket.IO connection
        const socket = io();
        
        // DOM elements
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const chatForm = document.getElementById('chatForm');
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');
        const typingIndicator = document.getElementById('typingIndicator');

        // Connection status
        socket.on('connect', function() {
            statusDot.classList.add('connected');
            statusText.textContent = 'Connected';
        });

        socket.on('disconnect', function() {
            statusDot.classList.remove('connected');
            statusText.textContent = 'Disconnected';
        });

        socket.on('connected', function(data) {
            console.log('Connected to server:', data);
            // Show a subtle connection notification
            if (data.mode === 'demo') {
                addSystemMessage('Connected to Indici Assistant Demo Mode', 'info');
            }
        });

        // Handle form submission
        chatForm.addEventListener('submit', function(e) {
            e.preventDefault();
            sendMessage();
        });

        // Send message function
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Add user message to chat
            addMessage(message, 'user');
            
            // Clear input and disable send button
            messageInput.value = '';
            sendButton.disabled = true;
            
            // Show typing indicator
            showTypingIndicator();

            // Send message to server
            socket.emit('message', { message: message });
        }

        // Send example query
        function sendExample(query) {
            messageInput.value = query;
            sendMessage();
        }

        // Handle server response
        socket.on('response', function(data) {
            hideTypingIndicator();
            sendButton.disabled = false;
            
            const messageType = data.type || 'success';
            addMessage(data.message, 'bot', messageType);
        });

        // Add message to chat
        function addMessage(content, sender, type = 'success') {
            // Remove welcome message if it exists
            const welcomeMessage = document.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            if (sender === 'bot' && type !== 'success') {
                messageDiv.classList.add(type);
            }

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.innerHTML = sender === 'user' ? 'U' : 'IA';

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            // Format message content (preserve line breaks and basic formatting)
            const formattedContent = content.replace(/\n/g, '<br>');
            messageContent.innerHTML = formattedContent;

            const messageTime = document.createElement('div');
            messageTime.className = 'message-time';
            messageTime.textContent = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

            messageDiv.appendChild(avatar);
            const contentWrapper = document.createElement('div');
            contentWrapper.appendChild(messageContent);
            contentWrapper.appendChild(messageTime);
            messageDiv.appendChild(contentWrapper);

            chatMessages.appendChild(messageDiv);

            // Scroll to bottom smoothly
            chatMessages.scrollTo({
                top: chatMessages.scrollHeight,
                behavior: 'smooth'
            });
        }

        // Add system message
        function addSystemMessage(content, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message bot ${type}`;
            messageDiv.style.opacity = '0.8';
            messageDiv.style.fontSize = '0.8rem';

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.innerHTML = '<i class="fas fa-info-circle"></i>';
            avatar.style.fontSize = '0.8rem';

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.innerHTML = content;

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);
            chatMessages.appendChild(messageDiv);

            // Auto-remove system messages after 5 seconds
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.style.transition = 'opacity 0.5s ease';
                    messageDiv.style.opacity = '0';
                    setTimeout(() => {
                        if (messageDiv.parentNode) {
                            messageDiv.remove();
                        }
                    }, 500);
                }
            }, 5000);
        }

        // Show typing indicator
        function showTypingIndicator() {
            typingIndicator.style.display = 'block';
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Hide typing indicator
        function hideTypingIndicator() {
            typingIndicator.style.display = 'none';
        }

        // Focus on input when page loads
        window.addEventListener('load', function() {
            messageInput.focus();
        });

        // Handle Enter key in input
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    </script>
</body>
</html>
