#!/usr/bin/env python3
"""
Test final queries with improved patterns
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_final_queries():
    """Test final queries"""
    print("🧪 Testing Final Queries")
    print("=" * 60)
    
    try:
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Test queries
        test_queries = [
            "show me wards in demo785",
            "rooms in Surgical Ward",
            "show me available rooms"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{i}️⃣ Testing: '{query}'")
            print("-" * 50)
            
            try:
                result = await asyncio.wait_for(
                    chatbot.process_message('test-session', query),
                    timeout=25.0
                )
                
                print(f"Result type: {result.get('type')}")
                content = result.get('content', '')
                
                if result.get('type') == 'success':
                    print("✅ Query successful!")
                    # Check if it contains table
                    if '<table' in content:
                        print("✅ Contains table!")
                    else:
                        print("❌ No table found")
                else:
                    print("❌ Query failed")
                
                # Show first few lines of response
                lines = content.split('\n')[:3]
                for line in lines:
                    if line.strip():
                        print(f"Response: {line.strip()}")
                
            except asyncio.TimeoutError:
                print("⏱️ Query timeout")
            except Exception as e:
                print(f"❌ Query error: {e}")
        
        print(f"\n✅ Final queries test completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_final_queries())
