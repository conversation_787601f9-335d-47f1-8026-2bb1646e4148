#!/usr/bin/env python3
"""
Test updates with real existing entities
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_real_updates():
    """Test updates with real existing entities"""
    print("🧪 Testing Updates with Real Existing Entities")
    print("=" * 60)
    
    try:
        # Create fresh instances
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Get current entities
        print("1️⃣ Getting current entities:")
        buildings = await client.get_buildings()
        wards = await client.get_wards()
        rooms = await client.get_rooms()
        
        print(f"Buildings: {len(buildings)}")
        if buildings:
            print(f"   First building: '{buildings[0].get('localityTitle')}'")
        
        print(f"Wards: {len(wards)}")
        if wards:
            print(f"   First ward: '{wards[0].get('localityTitle')}'")
        
        print(f"Rooms: {len(rooms)}")
        if rooms:
            print(f"   First room: '{rooms[0].get('localityTitle')}'")
        
        # Test ward update with existing ward
        if wards:
            print(f"\n2️⃣ Testing ward update:")
            test_ward = wards[0]['localityTitle']
            new_ward_name = f"TestWard{int(asyncio.get_event_loop().time())}"
            
            print(f"   Updating ward '{test_ward}' to '{new_ward_name}'")
            
            try:
                result = await asyncio.wait_for(
                    chatbot.process_message('test-session', f'update ward name {test_ward} to {new_ward_name}'),
                    timeout=15.0
                )
                
                print(f"   Result type: {result.get('type')}")
                if result.get('type') == 'success':
                    print(f"   ✅ Ward update SUCCESS!")
                else:
                    print(f"   ❌ Ward update failed:")
                    content = result.get('content', '')
                    print(f"   Error: {content[:200]}...")
                    
                    # Check if it's calling the right endpoint
                    if '/wards/update-by-name/' in content:
                        print(f"   ✅ Correctly calling ward endpoint")
                    elif '/buildings/update-by-name/' in content:
                        print(f"   ❌ Incorrectly calling building endpoint")
                    elif '/rooms/update-by-name/' in content:
                        print(f"   ❌ Incorrectly calling room endpoint")
                        
            except asyncio.TimeoutError:
                print(f"   ⏱️ Ward update timeout")
            except Exception as e:
                print(f"   ❌ Ward update error: {e}")
        
        # Test building update with existing building
        if buildings:
            print(f"\n3️⃣ Testing building update:")
            test_building = buildings[0]['localityTitle']
            new_building_name = f"TestBuilding{int(asyncio.get_event_loop().time())}"
            
            print(f"   Updating building '{test_building}' to '{new_building_name}'")
            
            try:
                result = await asyncio.wait_for(
                    chatbot.process_message('test-session', f'update building name {test_building} to {new_building_name}'),
                    timeout=15.0
                )
                
                print(f"   Result type: {result.get('type')}")
                if result.get('type') == 'success':
                    print(f"   ✅ Building update SUCCESS!")
                else:
                    print(f"   ❌ Building update failed:")
                    content = result.get('content', '')
                    print(f"   Error: {content[:200]}...")
                    
                    # Check if it's calling the right endpoint
                    if '/buildings/update-by-name/' in content:
                        print(f"   ✅ Correctly calling building endpoint")
                    elif '/wards/update-by-name/' in content:
                        print(f"   ❌ Incorrectly calling ward endpoint")
                    elif '/rooms/update-by-name/' in content:
                        print(f"   ❌ Incorrectly calling room endpoint")
                        
            except asyncio.TimeoutError:
                print(f"   ⏱️ Building update timeout")
            except Exception as e:
                print(f"   ❌ Building update error: {e}")
        
        # Test room update with existing room
        if rooms:
            print(f"\n4️⃣ Testing room update:")
            test_room = rooms[0]['localityTitle']
            new_room_name = f"TestRoom{int(asyncio.get_event_loop().time())}"
            
            print(f"   Updating room '{test_room}' to '{new_room_name}'")
            
            try:
                result = await asyncio.wait_for(
                    chatbot.process_message('test-session', f'update room name {test_room} to {new_room_name}'),
                    timeout=15.0
                )
                
                print(f"   Result type: {result.get('type')}")
                if result.get('type') == 'success':
                    print(f"   ✅ Room update SUCCESS!")
                else:
                    print(f"   ❌ Room update failed:")
                    content = result.get('content', '')
                    print(f"   Error: {content[:200]}...")
                    
                    # Check if it's calling the right endpoint
                    if '/rooms/update-by-name/' in content:
                        print(f"   ✅ Correctly calling room endpoint")
                    elif '/buildings/update-by-name/' in content:
                        print(f"   ❌ Incorrectly calling building endpoint")
                    elif '/wards/update-by-name/' in content:
                        print(f"   ❌ Incorrectly calling ward endpoint")
                        
            except asyncio.TimeoutError:
                print(f"   ⏱️ Room update timeout")
            except Exception as e:
                print(f"   ❌ Room update error: {e}")
        
        print(f"\n✅ All real update tests completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_real_updates())
