#!/usr/bin/env python3
"""
Test all new features with shorter timeouts
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_final_all_features():
    """Test all new features"""
    print("🧪 Testing All New Features")
    print("=" * 60)
    
    try:
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Test queries with shorter timeouts
        test_queries = [
            ("show me wards in demo785", "Wards by building"),
            ("rooms in Surgical Ward", "Rooms by ward"),
            ("show me available rooms", "Available rooms")
        ]
        
        for i, (query, description) in enumerate(test_queries, 1):
            print(f"\n{i}️⃣ Testing {description}: '{query}'")
            print("-" * 50)
            
            try:
                result = await asyncio.wait_for(
                    chatbot.process_message('test-session', query),
                    timeout=15.0  # Shorter timeout
                )
                
                print(f"Result type: {result.get('type')}")
                content = result.get('content', '')
                
                if result.get('type') == 'success':
                    print("✅ Query successful!")
                    # Check if it contains table
                    if '<table' in content:
                        print("✅ Contains table!")
                    else:
                        print("❌ No table found")
                    
                    # Check specific content
                    if 'wards in building' in content.lower():
                        print("✅ Shows wards by building!")
                    elif 'rooms in ward' in content.lower():
                        print("✅ Shows rooms by ward!")
                    elif 'available rooms' in content.lower():
                        print("✅ Shows available rooms!")
                    else:
                        print("? Unknown response type")
                else:
                    print("❌ Query failed")
                
                # Show first line of response
                first_line = content.split('\n')[0] if content else "No content"
                print(f"Response: {first_line}")
                
            except asyncio.TimeoutError:
                print("⏱️ Query timeout (API may be slow)")
            except Exception as e:
                print(f"❌ Query error: {str(e)[:100]}...")
        
        print(f"\n✅ All features test completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_final_all_features())
