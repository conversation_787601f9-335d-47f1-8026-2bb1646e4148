"""
IPU Management System API Client

Provides async HTTP client for interacting with the IPU Management System API.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Union
import httpx
from config import config, get_api_url, get_user_agent

logger = logging.getLogger(__name__)


class IPUAPIError(Exception):
    """Custom exception for IPU API errors"""
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class IPUClient:
    """Async client for IPU Management System API"""
    
    def __init__(self, base_url: Optional[str] = None, timeout: Optional[int] = None):
        self.base_url = (base_url or config.ipu.base_url).rstrip('/')
        self.timeout = timeout or config.ipu.timeout
        self.max_retries = config.ipu.max_retries
        
        # Create HTTP client with proper configuration
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.timeout),
            headers={
                "User-Agent": get_user_agent(),
                "Accept": "application/json",
                "Content-Type": "application/json"
            },
            follow_redirects=True
        )
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
    
    async def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request with retry logic and error handling"""
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        for attempt in range(self.max_retries + 1):
            try:
                logger.debug(f"Making {method} request to {url} (attempt {attempt + 1})")
                
                response = await self.client.request(method, url, **kwargs)
                
                # Log response details
                logger.debug(f"Response status: {response.status_code}")
                logger.debug(f"Response headers: {dict(response.headers)}")
                
                # Handle different response status codes
                if response.status_code == 200:
                    data = response.json()
                    logger.debug(f"Response data: {data}")
                    return data
                elif response.status_code == 404:
                    raise IPUAPIError(f"Resource not found: {url}", response.status_code)
                elif response.status_code >= 500:
                    if attempt < self.max_retries:
                        wait_time = 2 ** attempt
                        logger.warning(f"Server error {response.status_code}, retrying in {wait_time}s...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        raise IPUAPIError(f"Server error: {response.status_code}", response.status_code)
                else:
                    response.raise_for_status()
                    
            except httpx.TimeoutException:
                if attempt < self.max_retries:
                    wait_time = 2 ** attempt
                    logger.warning(f"Request timeout, retrying in {wait_time}s...")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    raise IPUAPIError(f"Request timeout after {self.max_retries} retries")
            except httpx.ConnectError:
                raise IPUAPIError(f"Cannot connect to IPU API at {self.base_url}")
            except Exception as e:
                if attempt < self.max_retries:
                    wait_time = 2 ** attempt
                    logger.warning(f"Request failed: {e}, retrying in {wait_time}s...")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    raise IPUAPIError(f"Request failed: {str(e)}")
        
        raise IPUAPIError("Max retries exceeded")
    
    def _extract_data(self, response: Dict[str, Any]) -> Any:
        """Extract data from IPU-API response format: ApiResponse<T>"""
        # IPU-API uses format: { "success": bool, "message": string, "data": T, "timestamp": datetime }
        if response.get('success'):
            return response.get('data', [])
        else:
            error_msg = response.get('message', 'Unknown API error')
            raise IPUAPIError(error_msg, response_data=response)
    
    # Building Management (LocalityTypeID = 1)
    async def get_buildings(self) -> List[Dict[str, Any]]:
        """Get all buildings from /api/Configuration/buildings"""
        try:
            response = await self._make_request("GET", "/api/Configuration/buildings")
            return self._extract_data(response)
        except Exception as e:
            logger.error(f"Error getting buildings: {e}")
            raise

    async def get_building_by_id(self, building_id: int) -> Optional[Dict[str, Any]]:
        """Get building by ID from /api/Configuration/buildings/{id}"""
        try:
            response = await self._make_request("GET", f"/api/Configuration/buildings/{building_id}")
            return self._extract_data(response)
        except IPUAPIError as e:
            if e.status_code == 404:
                return None
            raise
        except Exception as e:
            logger.error(f"Error getting building {building_id}: {e}")
            raise

    async def search_buildings(self, search_term: str) -> List[Dict[str, Any]]:
        """Search buildings by name"""
        try:
            # URL encode the search term
            import urllib.parse
            encoded_term = urllib.parse.quote(search_term)
            response = await self._make_request("GET", f"/api/Configuration/buildings/search?searchTerm={encoded_term}")
            return self._extract_data(response)
        except Exception as e:
            logger.warning(f"API search failed for buildings with term '{search_term}': {e}")
            # Fallback to manual filtering
            buildings = await self.get_buildings()
            search_lower = search_term.lower()
            return [
                building for building in buildings
                if (search_lower in building.get('localityTitle', '').lower() or
                    search_lower in building.get('localityCode', '').lower() or
                    search_lower in building.get('localityDescription', '').lower())
            ]

    async def create_building(self, building_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new building using CreateLocalityDto"""
        try:
            response = await self._make_request("POST", "/api/Configuration/buildings", json=building_data)
            return self._extract_data(response)
        except Exception as e:
            logger.error(f"Error creating building: {e}")
            raise

    async def update_building(self, building_id: int, building_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update an existing building using UpdateLocalityDto"""
        try:
            response = await self._make_request("PUT", f"/api/Configuration/buildings/{building_id}", json=building_data)
            return self._extract_data(response)
        except Exception as e:
            logger.error(f"Error updating building {building_id}: {e}")
            raise

    async def update_building_by_name(self, building_name: str, building_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update building by name using UpdateLocalityDto"""
        try:
            import urllib.parse
            encoded_name = urllib.parse.quote(building_name)
            response = await self._make_request("PUT", f"/api/Configuration/buildings/update-by-name/{encoded_name}", json=building_data)
            return self._extract_data(response)
        except Exception as e:
            logger.error(f"Error updating building by name {building_name}: {e}")
            raise

    async def delete_building(self, building_id: int) -> bool:
        """Delete a building"""
        try:
            response = await self._make_request("DELETE", f"/api/Configuration/buildings/{building_id}")
            return response.get('success', False)
        except Exception as e:
            logger.error(f"Error deleting building {building_id}: {e}")
            raise
    
    # Ward Management (LocalityTypeID = 2)
    async def get_wards(self) -> List[Dict[str, Any]]:
        """Get all wards from /api/Configuration/wards"""
        try:
            response = await self._make_request("GET", "/api/Configuration/wards")
            return self._extract_data(response)
        except Exception as e:
            logger.error(f"Error getting wards: {e}")
            raise

    async def get_wards_by_building(self, building_id: int) -> List[Dict[str, Any]]:
        """Get wards by building ID"""
        try:
            response = await self._make_request("GET", f"/api/Configuration/buildings/{building_id}/wards")
            return self._extract_data(response)
        except Exception as e:
            logger.error(f"Error getting wards for building {building_id}: {e}")
            raise

    async def update_ward_by_name(self, ward_name: str, ward_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update ward by name using UpdateLocalityDto"""
        try:
            import urllib.parse
            encoded_name = urllib.parse.quote(ward_name)
            response = await self._make_request("PUT", f"/api/Configuration/wards/update-by-name/{encoded_name}", json=ward_data)
            return self._extract_data(response)
        except Exception as e:
            logger.error(f"Error updating ward by name {ward_name}: {e}")
            raise

    async def search_wards(self, search_term: str) -> List[Dict[str, Any]]:
        """Search wards by name"""
        try:
            import urllib.parse
            encoded_term = urllib.parse.quote(search_term)
            response = await self._make_request("GET", f"/api/Configuration/wards/search?searchTerm={encoded_term}")
            return self._extract_data(response)
        except Exception as e:
            logger.warning(f"API search failed for wards with term '{search_term}': {e}")
            # Fallback to manual filtering
            wards = await self.get_wards()
            search_lower = search_term.lower()
            return [
                ward for ward in wards
                if (search_lower in ward.get('localityTitle', '').lower() or
                    search_lower in ward.get('localityCode', '').lower() or
                    search_lower in ward.get('localityDescription', '').lower())
            ]

    # Room Management (LocalityTypeID = 3)
    async def get_rooms(self) -> List[Dict[str, Any]]:
        """Get all rooms from /api/Configuration/rooms"""
        try:
            response = await self._make_request("GET", "/api/Configuration/rooms")
            return self._extract_data(response)
        except Exception as e:
            logger.error(f"Error getting rooms: {e}")
            raise

    async def get_available_rooms(self) -> List[Dict[str, Any]]:
        """Get available (not occupied) rooms"""
        try:
            response = await self._make_request("GET", "/api/Configuration/rooms/available")
            return self._extract_data(response)
        except Exception as e:
            logger.error(f"Error getting available rooms: {e}")
            raise

    async def get_rooms_by_ward(self, ward_id: int) -> List[Dict[str, Any]]:
        """Get rooms by ward ID"""
        try:
            response = await self._make_request("GET", f"/api/Configuration/wards/{ward_id}/rooms")
            return self._extract_data(response)
        except Exception as e:
            logger.error(f"Error getting rooms for ward {ward_id}: {e}")
            raise

    async def get_wards_by_building(self, building_id: int) -> List[Dict[str, Any]]:
        """Get wards by building ID"""
        try:
            response = await self._make_request("GET", f"/api/Configuration/buildings/{building_id}/wards")
            return self._extract_data(response)
        except Exception as e:
            logger.error(f"Error getting wards for building {building_id}: {e}")
            raise

    async def get_wards_by_building_name(self, building_name: str) -> List[Dict[str, Any]]:
        """Get wards by building name"""
        try:
            # First, find the building by name
            buildings = await self.get_buildings()
            building = None
            for b in buildings:
                if b.get('localityTitle', '').lower() == building_name.lower():
                    building = b
                    break

            if not building:
                raise ValueError(f"Building '{building_name}' not found")

            # Get wards for this building
            building_id = building.get('localityID')
            return await self.get_wards_by_building(building_id)
        except Exception as e:
            logger.error(f"Error getting wards for building '{building_name}': {e}")
            raise

    async def get_rooms_by_ward_name(self, ward_name: str) -> List[Dict[str, Any]]:
        """Get rooms by ward name"""
        try:
            # First, find the ward by name
            wards = await self.get_wards()
            ward = None
            for w in wards:
                if w.get('localityTitle', '').lower() == ward_name.lower():
                    ward = w
                    break

            if not ward:
                raise ValueError(f"Ward '{ward_name}' not found")

            # Get rooms for this ward
            ward_id = ward.get('localityID')
            return await self.get_rooms_by_ward(ward_id)
        except Exception as e:
            logger.error(f"Error getting rooms for ward '{ward_name}': {e}")
            raise

    async def update_room_by_name(self, room_name: str, room_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update room by name using UpdateLocalityDto"""
        try:
            import urllib.parse
            encoded_name = urllib.parse.quote(room_name)
            response = await self._make_request("PUT", f"/api/Configuration/rooms/update-by-name/{encoded_name}", json=room_data)
            return self._extract_data(response)
        except Exception as e:
            logger.error(f"Error updating room by name {room_name}: {e}")
            raise

    # Delete Operations by Name
    async def delete_ward_by_name(self, ward_name: str) -> Dict[str, Any]:
        """Delete ward by name"""
        try:
            import urllib.parse
            encoded_name = urllib.parse.quote(ward_name)
            response = await self._make_request("DELETE", f"/api/Configuration/wards/delete-by-name/{encoded_name}")
            return response
        except Exception as e:
            logger.error(f"Error deleting ward '{ward_name}': {e}")
            raise

    async def delete_room_by_name(self, room_name: str) -> Dict[str, Any]:
        """Delete room by name"""
        try:
            import urllib.parse
            encoded_name = urllib.parse.quote(room_name)
            response = await self._make_request("DELETE", f"/api/Configuration/rooms/delete-by-name/{encoded_name}")
            return response
        except Exception as e:
            logger.error(f"Error deleting room '{room_name}': {e}")
            raise

    async def search_rooms(self, search_term: str) -> List[Dict[str, Any]]:
        """Search rooms by name"""
        try:
            import urllib.parse
            encoded_term = urllib.parse.quote(search_term)
            response = await self._make_request("GET", f"/api/Configuration/rooms/search?searchTerm={encoded_term}")
            return self._extract_data(response)
        except Exception as e:
            logger.warning(f"API search failed for rooms with term '{search_term}': {e}")
            # Fallback to manual filtering
            rooms = await self.get_rooms()
            search_lower = search_term.lower()
            return [
                room for room in rooms
                if (search_lower in room.get('localityTitle', '').lower() or
                    search_lower in room.get('localityCode', '').lower() or
                    search_lower in room.get('localityDescription', '').lower())
            ]

    async def create_room(self, room_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new room using CreateLocalityDto"""
        try:
            response = await self._make_request("POST", "/api/Configuration/rooms", json=room_data)
            return self._extract_data(response)
        except Exception as e:
            logger.error(f"Error creating room: {e}")
            raise

    # Simple Create Operations
    async def create_building_simple(self, building_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new building using simple endpoint"""
        try:
            response = await self._make_request("POST", "/api/Configuration/buildings/simple", json=building_data)
            return response
        except Exception as e:
            logger.error(f"Error creating building (simple): {e}")
            raise

    async def create_ward_simple(self, ward_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new ward using simple endpoint"""
        try:
            response = await self._make_request("POST", "/api/Configuration/wards/simple", json=ward_data)
            return response
        except Exception as e:
            logger.error(f"Error creating ward (simple): {e}")
            raise

    async def create_room_simple(self, room_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new room using simple endpoint"""
        try:
            response = await self._make_request("POST", "/api/Configuration/rooms/simple", json=room_data)
            return response
        except Exception as e:
            logger.error(f"Error creating room (simple): {e}")
            raise

    async def update_room(self, room_id: int, room_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update an existing room using UpdateLocalityDto"""
        try:
            response = await self._make_request("PUT", f"/api/Configuration/rooms/{room_id}", json=room_data)
            return self._extract_data(response)
        except Exception as e:
            logger.error(f"Error updating room {room_id}: {e}")
            raise

    async def delete_room(self, room_id: int) -> bool:
        """Delete a room"""
        try:
            response = await self._make_request("DELETE", f"/api/Configuration/rooms/{room_id}")
            return response.get('success', False)
        except Exception as e:
            logger.error(f"Error deleting room {room_id}: {e}")
            raise

    # Search and Filter Operations
    async def search_all_localities(self, search_term: str, locality_type_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """Search all localities (buildings, wards, rooms) by name"""
        try:
            import urllib.parse
            encoded_term = urllib.parse.quote(search_term)
            params = f"searchTerm={encoded_term}"
            if locality_type_id:
                params += f"&localityTypeId={locality_type_id}"
            response = await self._make_request("GET", f"/api/configuration/search?{params}")
            return self._extract_data(response)
        except Exception as e:
            logger.warning(f"API search failed for localities with term '{search_term}': {e}")
            # Fallback to searching each type individually
            results = []
            try:
                if not locality_type_id or locality_type_id == 1:
                    results.extend(await self.search_buildings(search_term))
                if not locality_type_id or locality_type_id == 2:
                    results.extend(await self.search_wards(search_term))
                if not locality_type_id or locality_type_id == 3:
                    results.extend(await self.search_rooms(search_term))
            except Exception as fallback_error:
                logger.error(f"Fallback search also failed: {fallback_error}")
                raise
            return results

    async def get_isolation_rooms(self) -> List[Dict[str, Any]]:
        """Get isolation rooms by filtering available rooms"""
        try:
            # Since the API doesn't have specific isolation endpoint,
            # we'll filter rooms based on facilities or description
            rooms = await self.get_rooms()
            isolation_rooms = [
                room for room in rooms
                if ('isolation' in room.get('localityTitle', '').lower() or
                    'isolation' in room.get('localityDescription', '').lower() or
                    'isolation' in room.get('facilities', '').lower())
            ]
            return isolation_rooms
        except Exception as e:
            logger.error(f"Error getting isolation rooms: {e}")
            raise

    async def get_homecare_rooms(self) -> List[Dict[str, Any]]:
        """Get home care rooms by filtering rooms with IsHomeLeave=true"""
        try:
            rooms = await self.get_rooms()
            homecare_rooms = [
                room for room in rooms
                if room.get('isHomeLeave', False)
            ]
            return homecare_rooms
        except Exception as e:
            logger.error(f"Error getting home care rooms: {e}")
            raise
    
    # Analytics and Statistics
    async def get_occupancy_stats(self) -> Dict[str, Any]:
        """Calculate occupancy statistics from available data"""
        try:
            all_rooms = await self.get_rooms()
            available_rooms = await self.get_available_rooms()

            total_rooms = len(all_rooms)
            available_count = len(available_rooms)
            occupied_count = total_rooms - available_count
            occupancy_rate = (occupied_count / total_rooms * 100) if total_rooms > 0 else 0

            return {
                'totalRooms': total_rooms,
                'availableRooms': available_count,
                'occupiedRooms': occupied_count,
                'occupancyRate': round(occupancy_rate, 2)
            }
        except Exception as e:
            logger.error(f"Error calculating occupancy stats: {e}")
            raise

    # Health check
    async def health_check(self) -> bool:
        """Check if the IPU API is healthy"""
        try:
            # Use the dedicated health endpoint
            response = await self._make_request("GET", "/health")
            return response.get('status') == 'Healthy'
        except Exception as e:
            logger.warning(f"Health check failed: {e}")
            # Fallback: try to get buildings as a health check
            try:
                await self.get_buildings()
                return True
            except Exception:
                return False
            logger.info("IPU API health check passed")
            return True
        except Exception as e:
            logger.error(f"IPU API health check failed: {e}")
            return False


# Convenience function for creating client
def create_client(base_url: Optional[str] = None, timeout: Optional[int] = None) -> IPUClient:
    """Create a new IPU client instance"""
    return IPUClient(base_url, timeout)


# Global client instance for convenience
_global_client: Optional[IPUClient] = None


async def get_global_client() -> IPUClient:
    """Get or create global client instance"""
    global _global_client
    if _global_client is None:
        _global_client = create_client()
    return _global_client


async def close_global_client():
    """Close global client instance"""
    global _global_client
    if _global_client is not None:
        await _global_client.close()
        _global_client = None
