# Claude Desktop Integration

This guide shows how to integrate the IPU MCP Server with <PERSON> for natural language access to your hospital management system.

## Prerequisites

1. **<PERSON> Des<PERSON>op** installed
2. **IPU MCP Server** working (test with `python test_mcp.py`)
3. **IPU Management System API** running

## Configuration

### 1. Locate Claude Desktop Config

The configuration file location depends on your operating system:

**Windows:**
```
%APPDATA%\Claude\claude_desktop_config.json
```

**macOS:**
```
~/Library/Application Support/Claude/claude_desktop_config.json
```

**Linux:**
```
~/.config/Claude/claude_desktop_config.json
```

### 2. Add IPU MCP Server

Edit the `claude_desktop_config.json` file and add the IPU server:

```json
{
  "mcpServers": {
    "ipu-management": {
      "command": "python",
      "args": ["/path/to/IPU-MCP-Chatbot/mcp_server.py"],
      "env": {
        "PYTHONPATH": "/path/to/IPU-MCP-Chatbot",
        "IPU_API_URL": "http://localhost:5077"
      }
    }
  }
}
```

**Important:** Replace `/path/to/IPU-MCP-Chatbot` with the actual path to your project directory.

### 3. Alternative Configuration (Using Virtual Environment)

If you're using a Python virtual environment:

```json
{
  "mcpServers": {
    "ipu-management": {
      "command": "/path/to/venv/bin/python",
      "args": ["/path/to/IPU-MCP-Chatbot/mcp_server.py"],
      "env": {
        "PYTHONPATH": "/path/to/IPU-MCP-Chatbot"
      }
    }
  }
}
```

### 4. Windows PowerShell Configuration

For Windows with PowerShell:

```json
{
  "mcpServers": {
    "ipu-management": {
      "command": "powershell",
      "args": [
        "-Command", 
        "cd 'C:\\path\\to\\IPU-MCP-Chatbot'; python mcp_server.py"
      ],
      "env": {
        "IPU_API_URL": "http://localhost:5077"
      }
    }
  }
}
```

## Testing the Integration

### 1. Restart Claude Desktop

After editing the configuration file, restart Claude Desktop completely.

### 2. Verify Connection

In Claude Desktop, you should see the IPU tools available. Try asking:

```
"What tools do you have available for hospital management?"
```

Claude should list the IPU management tools.

### 3. Test Basic Functionality

Try these example queries:

```
"Show me all the buildings in the hospital"
"What's the current occupancy rate?"
"Find all available isolation rooms"
"Search for rooms with cardiac equipment"
"Give me statistics about the hospital system"
```

## Example Conversations

### Getting Occupancy Information
**You:** "What's the current occupancy rate in the hospital?"

**Claude:** *Uses get_occupancy_stats tool and provides formatted response*

### Finding Available Rooms
**You:** "I need to find available rooms for a new patient"

**Claude:** *Uses get_available_rooms tool and shows available options*

### Searching for Specific Equipment
**You:** "Find all rooms with ventilator equipment"

**Claude:** *Uses search_rooms tool with "ventilator" query*

### System Overview
**You:** "Give me an overview of the hospital system"

**Claude:** *Uses get_system_stats tool and provides comprehensive summary*

## Troubleshooting

### Tools Not Appearing

1. **Check Configuration Path**: Ensure the path to `mcp_server.py` is correct
2. **Verify Python Path**: Make sure Python can find all dependencies
3. **Test Manually**: Run `python mcp_server.py` manually to check for errors
4. **Check Logs**: Look at Claude Desktop logs for error messages

### Connection Errors

1. **API Running**: Ensure IPU API is running on `http://localhost:5077`
2. **Network Access**: Verify Claude can access the API
3. **Firewall**: Check firewall settings
4. **Environment Variables**: Verify `IPU_API_URL` is set correctly

### Permission Issues

1. **File Permissions**: Ensure Claude can execute the Python script
2. **Python Installation**: Verify Python is in the system PATH
3. **Virtual Environment**: If using venv, ensure the path is correct

## Advanced Configuration

### Custom Environment Variables

```json
{
  "mcpServers": {
    "ipu-management": {
      "command": "python",
      "args": ["/path/to/IPU-MCP-Chatbot/mcp_server.py"],
      "env": {
        "IPU_API_URL": "http://localhost:5077",
        "LOG_LEVEL": "DEBUG",
        "IPU_API_TIMEOUT": "60"
      }
    }
  }
}
```

### Multiple Environments

You can configure different environments:

```json
{
  "mcpServers": {
    "ipu-development": {
      "command": "python",
      "args": ["/path/to/IPU-MCP-Chatbot/mcp_server.py"],
      "env": {
        "IPU_API_URL": "http://localhost:5077"
      }
    },
    "ipu-production": {
      "command": "python",
      "args": ["/path/to/IPU-MCP-Chatbot/mcp_server.py"],
      "env": {
        "IPU_API_URL": "https://production-api.hospital.com"
      }
    }
  }
}
```

## Security Considerations

1. **Read-Only Access**: The MCP server only reads data from the IPU API
2. **Local Network**: Keep the API on a secure local network
3. **Authentication**: Consider adding API authentication if needed
4. **Logging**: Monitor access logs for security

## Best Practices

1. **Test First**: Always test with `python test_mcp.py` before configuring Claude
2. **Use Virtual Environments**: Isolate Python dependencies
3. **Monitor Performance**: Watch for slow API responses
4. **Regular Updates**: Keep dependencies updated
5. **Backup Configuration**: Save your Claude configuration

Your IPU Management System is now integrated with Claude Desktop! 🎉
