#!/usr/bin/env python3
"""
Template for adding new API functionality to IPU MCP Chatbot
Copy this template and modify for your new function
"""

# =============================================================================
# STEP 1: ADD TO ipu_client.py
# =============================================================================

"""
Add this method to the IPUClient class in ipu_client.py:
"""

async def your_new_function_name(self, param1: str, param2: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Description of what this function does
    
    Args:
        param1: Description of parameter 1
        param2: Description of parameter 2 (optional)
    
    Returns:
        Dict containing the API response
    """
    try:
        # Choose the appropriate HTTP method and endpoint:
        
        # For GET requests:
        response = await self._make_request("GET", f"/api/Configuration/your-endpoint/{param1}")
        
        # For POST requests with data:
        # response = await self._make_request("POST", "/api/Configuration/your-endpoint", json=param2)
        
        # For PUT requests:
        # response = await self._make_request("PUT", f"/api/Configuration/your-endpoint/{param1}", json=param2)
        
        # For DELETE requests:
        # response = await self._make_request("DELETE", f"/api/Configuration/your-endpoint/{param1}")
        
        # URL encoding for names with spaces:
        # import urllib.parse
        # encoded_name = urllib.parse.quote(param1)
        # response = await self._make_request("GET", f"/api/Configuration/your-endpoint/{encoded_name}")
        
        return response  # or self._extract_data(response) for data extraction
    except Exception as e:
        logger.error(f"Error in your_new_function_name: {e}")
        raise

# =============================================================================
# STEP 2: ADD TO llm_service.py - Function Registry (around line 168-251)
# =============================================================================

"""
Add this entry to the self.functions dictionary in FunctionRegistry.__init__():
"""

"your_new_function_name": {
    "name": "your_new_function_name",
    "description": "Clear description of what this function does",
    "parameters": {
        "type": "object",
        "properties": {
            "param1": {
                "type": "string",  # or "object", "integer", "boolean"
                "description": "Description of parameter 1"
            },
            "param2": {
                "type": "object",
                "description": "Description of parameter 2"
            }
        },
        "required": ["param1"]  # List all required parameters
    }
},

# =============================================================================
# STEP 3: ADD TO llm_service.py - Execution Logic (around line 397-410)
# =============================================================================

"""
Add this elif block to the execute_function method:
"""

elif function_name == "your_new_function_name":
    result = await self.ipu_client.your_new_function_name(
        arguments["param1"], 
        arguments.get("param2")  # Use .get() for optional parameters
    )

# =============================================================================
# STEP 4: ADD TO llm_service.py - Message Handling (around line 1308+)
# =============================================================================

"""
Add this elif block to the _mock_response method:
"""

# Handle your new functionality
elif any(keyword in user_lower for keyword in ["your", "trigger", "keywords"]):
    # Extract parameters from user message using regex
    import re
    patterns = [
        r'your regex pattern to extract (\w+)',
        r'alternative pattern (\w+) with optional (\w+)',
        r'(?:command|action)\s+([a-zA-Z0-9_\-\s]+?)(?:\s|$)'  # For multi-word extraction
    ]
    
    param1 = None
    param2 = None
    
    for pattern in patterns:
        match = re.search(pattern, user_message, re.IGNORECASE)
        if match:
            param1 = match.group(1).strip()
            param2 = match.group(2).strip() if match.lastindex >= 2 else None
            break
    
    if param1 and self.function_registry:
        try:
            # Prepare parameters for the function call
            function_params = {"param1": param1}
            if param2:
                function_params["param2"] = {"some_key": param2}  # Adjust structure as needed
            
            result = await self.function_registry.execute_function("your_new_function_name", function_params)
            
            if result.get("success"):
                # Success response
                return {
                    "content": f"""✅ **Operation Successful**

**Parameter 1:** {param1}
**Parameter 2:** {param2 or 'Not provided'}

The operation completed successfully.""",
                    "type": "success"
                }
            else:
                # Error response
                return {
                    "content": f"""❌ **Operation Failed**

Could not complete operation with "{param1}".

Error: {result.get('message', 'Operation failed')}""",
                    "type": "error"
                }
        except Exception as e:
            return {
                "content": f"❌ Error processing request: {str(e)}",
                "type": "error"
            }

# =============================================================================
# STEP 5: CREATE TEST FILE
# =============================================================================

"""
Create a new file: test_your_new_function.py
"""

#!/usr/bin/env python3
"""
Test your new function
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_your_new_function():
    """Test your new function"""
    print("🧪 Testing Your New Function")
    print("=" * 60)
    
    try:
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Test direct API call
        print("1️⃣ Testing direct API call:")
        try:
            result = await client.your_new_function_name("test_param")
            print(f"✅ Direct API: {result}")
        except Exception as e:
            print(f"❌ Direct API error: {e}")
        
        # Test chatbot integration
        test_queries = [
            "your test command here",
            "another test command with parameter",
            "third test command"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{i+1}️⃣ Testing: '{query}'")
            try:
                result = await asyncio.wait_for(
                    chatbot.process_message('test-session', query),
                    timeout=20.0
                )
                
                print(f"Result type: {result.get('type')}")
                content = result.get('content', '')
                first_line = content.split('\n')[0] if content else "No content"
                print(f"Response: {first_line}")
                
                if result.get('type') == 'success':
                    print("✅ Query successful!")
                else:
                    print("❌ Query failed")
                
            except Exception as e:
                print(f"❌ Error: {e}")
        
        print(f"\n✅ Test completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == '__main__':
    asyncio.run(test_your_new_function())

# =============================================================================
# COMMON REGEX PATTERNS
# =============================================================================

"""
Common regex patterns for extracting parameters:

# Single word/name:
r'(?:command|action)\s+(\w+)'

# Two parameters:
r'(?:command)\s+(\w+)\s+to\s+(\w+)'
r'(?:update|change)\s+(\w+)\s+to\s+(\w+)'

# Optional parameters:
r'(?:command)\s+(\w+)(?:\s+with\s+(\w+))?'

# Multi-word extraction:
r'(?:command)\s+([a-zA-Z0-9_\-\s]+?)(?:\s*$|\s+(?:and|or|,))'

# Complex patterns:
r'(?:please|can you)?\s*(?:command)\s+(?:the\s+)?(\w+)'
r'(?:command)\s+(?:name\s+)?([^\s]+)\s+to\s+([^\s]+)'
"""

# =============================================================================
# EXAMPLE NATURAL LANGUAGE COMMANDS
# =============================================================================

"""
Examples of natural language commands your function might handle:

"show me your_entity"
"list all your_entities"
"get your_entity details"
"create your_entity EntityName"
"update your_entity OldName to NewName"
"delete your_entity EntityName"
"search for your_entity SearchTerm"
"assign your_entity1 to your_entity2"
"move your_entity from Location1 to Location2"
"""

# =============================================================================
# CHECKLIST
# =============================================================================

"""
✅ Checklist for adding new functionality:

□ Step 1: Add API client method in ipu_client.py
□ Step 2: Add function to registry in llm_service.py
□ Step 3: Add execution logic in llm_service.py
□ Step 4: Add message handling in llm_service.py
□ Step 5: Create test file
□ Step 6: Test direct API calls
□ Step 7: Test chatbot integration
□ Step 8: Test natural language commands
□ Step 9: Handle edge cases and errors
□ Step 10: Document the new functionality
"""
