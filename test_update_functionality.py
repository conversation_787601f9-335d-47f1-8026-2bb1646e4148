#!/usr/bin/env python3
"""
Test the new update functionality with real API calls
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_update_functionality():
    """Test the new update functionality"""
    print("🧪 Testing Real Update Functionality")
    print("=" * 60)
    
    try:
        # Create client and chatbot
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Test 1: Show current buildings first
        print("\n1️⃣ Current Buildings:")
        print("-" * 40)
        result1 = await chatbot.process_message('test-session', 'Show me all buildings')
        if result1.get('type') == 'success':
            print("✅ Buildings retrieved successfully")
            # Look for test333 in the response
            if 'test333' in result1.get('content', ''):
                print("✅ Found 'test333' building in the list")
            else:
                print("⚠️ 'test333' building not found in current list")
        else:
            print("❌ Failed to retrieve buildings")
        
        # Test 2: Try to update building name
        print("\n2️⃣ Testing Building Update:")
        print("-" * 40)
        result2 = await chatbot.process_message('test-session', 'update building name test333 to Buildingone')
        print(f"Response Type: {result2.get('type')}")
        print("Response Content:")
        print(result2.get('content', 'No content')[:300] + "...")
        
        # Test 3: Check if update worked by showing buildings again
        if result2.get('type') == 'success':
            print("\n3️⃣ Verifying Update:")
            print("-" * 40)
            result3 = await chatbot.process_message('test-session', 'Show me all buildings')
            if 'Buildingone' in result3.get('content', ''):
                print("✅ Update successful! 'Buildingone' found in the list")
            else:
                print("⚠️ 'Buildingone' not found - update may not have worked")
        
        # Test 4: Test room update
        print("\n4️⃣ Testing Room Update:")
        print("-" * 40)
        result4 = await chatbot.process_message('test-session', 'update room name Room1 to NewRoom')
        print(f"Response Type: {result4.get('type')}")
        print("Response Content:")
        print(result4.get('content', 'No content')[:300] + "...")
        
        # Test 5: Test invalid update format
        print("\n5️⃣ Testing Invalid Format:")
        print("-" * 40)
        result5 = await chatbot.process_message('test-session', 'update building invalid format')
        print(f"Response Type: {result5.get('type')}")
        print("Response Content:")
        print(result5.get('content', 'No content')[:200] + "...")
        
        print("\n✅ All update functionality tests completed!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_update_functionality())
