"""
Room management tools for MCP
"""

from typing import Any, Dict, List
from mcp.types import Tool, TextContent
from .base_tool import BaseTool


class RoomTools(BaseTool):
    """Tools for room management"""
    
    def get_tool_definition(self) -> Tool:
        """Get rooms tool definition"""
        return Tool(
            name="get_rooms",
            description="Get all rooms in the IPU system",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute get rooms"""
        rooms = await self.client.get_rooms()

        if not rooms:
            return [TextContent(type="text", text="🛏️ No rooms found in the system.")]

        # Format rooms using actual API field names
        text = "🛏️ **Hospital Rooms**\n\n"
        for room in rooms:
            status_emoji = "🔴" if room.get('isOccupied', False) else "🟢"
            text += f"{status_emoji} **{room.get('localityTitle', 'Unknown')}** (ID: {room.get('localityID')})\n"
            text += f"• Code: {room.get('localityCode', 'N/A')}\n"
            if room.get('localityDescription'):
                text += f"• Description: {room.get('localityDescription')}\n"
            text += f"• Ward: {room.get('parentLocalityTitle', 'N/A')}\n"
            text += f"• Status: {'Occupied' if room.get('isOccupied', False) else 'Available'}\n"
            text += f"• Active: {'Yes' if room.get('isActive', False) else 'No'}\n"
            if room.get('facilities'):
                text += f"• Facilities: {room.get('facilities')}\n"
            text += "\n"

        return [TextContent(type="text", text=text)]


class RoomsByWardTools(BaseTool):
    """Tools for getting rooms by ward"""
    
    def get_tool_definition(self) -> Tool:
        """Get rooms by ward tool definition"""
        return Tool(
            name="get_rooms_by_ward",
            description="Get all rooms in a specific ward",
            inputSchema={
                "type": "object",
                "properties": {
                    "ward_id": {
                        "type": "integer",
                        "description": "The ID of the ward to get rooms for"
                    }
                },
                "required": ["ward_id"]
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute get rooms by ward"""
        # Validate arguments
        error = self.validate_required_args(arguments, ["ward_id"])
        if error:
            return self.create_error_response(error)
        
        error = self.validate_integer_arg(arguments, "ward_id")
        if error:
            return self.create_error_response(error)
        
        ward_id = int(arguments["ward_id"])
        
        # Get ward info first
        ward = await self.client.get_ward_by_id(ward_id)
        if not ward:
            return [TextContent(type="text", text=f"🏥 Ward with ID {ward_id} not found.")]
        
        # Get rooms
        rooms = await self.client.get_rooms_by_ward(ward_id)
        
        if not rooms:
            ward_name = ward.get('title', f'Ward {ward_id}')
            return [TextContent(type="text", text=f"🛏️ No rooms found in {ward_name}.")]
        
        # Format results
        ward_name = ward.get('title', f'Ward {ward_id}')
        summary = self.format_summary_response(
            rooms,
            ["title", "code", "type", "isOccupied"],
            f"🛏️ Rooms in {ward_name}"
        )
        
        return [TextContent(type="text", text=summary)]


class AvailableRoomsTools(BaseTool):
    """Tools for getting available rooms"""
    
    def get_tool_definition(self) -> Tool:
        """Get available rooms tool definition"""
        return Tool(
            name="get_available_rooms",
            description="Get all available (unoccupied) rooms in the IPU system",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute get available rooms"""
        rooms = await self.client.get_available_rooms()
        
        if not rooms:
            return [TextContent(type="text", text="🛏️ No available rooms found.")]
        
        # Format results
        summary = self.format_summary_response(
            rooms,
            ["title", "code", "type", "wardTitle"],
            "🛏️ Available Rooms"
        )
        
        return [TextContent(type="text", text=summary)]


class IsolationRoomsTools(BaseTool):
    """Tools for getting isolation rooms"""
    
    def get_tool_definition(self) -> Tool:
        """Get isolation rooms tool definition"""
        return Tool(
            name="get_isolation_rooms",
            description="Get all isolation rooms in the IPU system",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute get isolation rooms"""
        rooms = await self.client.get_isolation_rooms()
        
        if not rooms:
            return [TextContent(type="text", text="🦠 No isolation rooms found.")]
        
        # Format results with occupancy status
        summary = self.format_summary_response(
            rooms,
            ["title", "code", "isOccupied", "wardTitle"],
            "🦠 Isolation Rooms"
        )
        
        return [TextContent(type="text", text=summary)]


class HomeCareRoomsTools(BaseTool):
    """Tools for getting home care rooms"""
    
    def get_tool_definition(self) -> Tool:
        """Get home care rooms tool definition"""
        return Tool(
            name="get_homecare_rooms",
            description="Get all home care rooms in the IPU system",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute get home care rooms"""
        rooms = await self.client.get_homecare_rooms()
        
        if not rooms:
            return [TextContent(type="text", text="🏠 No home care rooms found.")]
        
        # Format results
        summary = self.format_summary_response(
            rooms,
            ["title", "code", "isOccupied", "wardTitle"],
            "🏠 Home Care Rooms"
        )
        
        return [TextContent(type="text", text=summary)]


class RoomSearchTools(BaseTool):
    """Tools for searching rooms"""
    
    def get_tool_definition(self) -> Tool:
        """Get room search tool definition"""
        return Tool(
            name="search_rooms",
            description="Search for rooms by title, code, type, or description",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query to find rooms"
                    }
                },
                "required": ["query"]
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute room search"""
        # Validate arguments
        error = self.validate_required_args(arguments, ["query"])
        if error:
            return self.create_error_response(error)
        
        query = arguments["query"].strip()
        if not query:
            return self.create_error_response("Search query cannot be empty")
        
        # Search rooms
        rooms = await self.client.search_rooms(query)
        
        if not rooms:
            return [TextContent(type="text", text=f"🔍 No rooms found matching '{query}'.")]
        
        # Format results
        summary = self.format_summary_response(
            rooms,
            ["title", "code", "type", "isOccupied"],
            f"🔍 Rooms matching '{query}'"
        )
        
        return [TextContent(type="text", text=summary)]


class RoomDetailsTools(BaseTool):
    """Tools for getting room details"""
    
    def get_tool_definition(self) -> Tool:
        """Get room details tool definition"""
        return Tool(
            name="get_room_details",
            description="Get detailed information about a specific room by ID",
            inputSchema={
                "type": "object",
                "properties": {
                    "room_id": {
                        "type": "integer",
                        "description": "The ID of the room to retrieve"
                    }
                },
                "required": ["room_id"]
            }
        )
    
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute get room details"""
        # Validate arguments
        error = self.validate_required_args(arguments, ["room_id"])
        if error:
            return self.create_error_response(error)
        
        error = self.validate_integer_arg(arguments, "room_id")
        if error:
            return self.create_error_response(error)
        
        room_id = int(arguments["room_id"])
        room = await self.client.get_room_by_id(room_id)
        
        if not room:
            return [TextContent(type="text", text=f"🛏️ Room with ID {room_id} not found.")]
        
        # Format room details
        occupancy_status = "🔴 Occupied" if room.get('isOccupied', False) else "🟢 Available"
        multiple_patients = "Yes" if room.get('isMultiplePatientAllowed', False) else "No"
        home_care = "Yes" if room.get('isHomeCareRoom', False) else "No"
        
        details = f"""🛏️ **Room Details**

**Name:** {room.get('title', 'N/A')}
**Code:** {room.get('code', 'N/A')}
**Type:** {room.get('type', 'N/A')}
**Ward:** {room.get('wardTitle', 'N/A')}
**Status:** {occupancy_status}
**Multiple Patients Allowed:** {multiple_patients}
**Home Care Room:** {home_care}
**Sequence:** {room.get('sequence', 'N/A')}
**Facilities:** {room.get('facilities', 'No facilities listed')}
**Description:** {room.get('description', 'No description available')}
**Active:** {'Yes' if room.get('isActive', False) else 'No'}
**Created:** {room.get('createdAt', 'N/A')}
**Last Updated:** {room.get('updatedAt', 'Never')}
"""
        
        return [TextContent(type="text", text=details)]


class UpdateRoomTools(BaseTool):
    """Tools for updating room information"""

    def get_tool_definition(self) -> Tool:
        """Get update room tool definition"""
        return Tool(
            name="update_room",
            description="Update room information by ID",
            inputSchema={
                "type": "object",
                "properties": {
                    "room_id": {
                        "type": "integer",
                        "description": "The ID of the room to update"
                    },
                    "name": {
                        "type": "string",
                        "description": "New room name (localityTitle)"
                    },
                    "code": {
                        "type": "string",
                        "description": "New room code (localityCode)"
                    },
                    "description": {
                        "type": "string",
                        "description": "New room description (localityDescription)"
                    },
                    "facilities": {
                        "type": "string",
                        "description": "New facilities information"
                    },
                    "is_occupied": {
                        "type": "boolean",
                        "description": "Room occupancy status"
                    }
                },
                "required": ["room_id"]
            }
        )

    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute update room"""
        room_id = arguments.get("room_id")

        if not room_id:
            return [TextContent(type="text", text="❌ Room ID is required")]

        # Prepare update data
        update_data = {}
        if arguments.get("name"):
            update_data["localityTitle"] = arguments["name"]
        if arguments.get("code"):
            update_data["localityCode"] = arguments["code"]
        if arguments.get("description"):
            update_data["localityDescription"] = arguments["description"]
        if arguments.get("facilities"):
            update_data["facilities"] = arguments["facilities"]
        if "is_occupied" in arguments:
            update_data["isOccupied"] = arguments["is_occupied"]

        if not update_data:
            return [TextContent(type="text", text="❌ At least one field must be provided for update")]

        try:
            # Update the room
            updated_room = await self.client.update_room(room_id, update_data)

            # Format success response
            status_emoji = "🔴" if updated_room.get('isOccupied', False) else "🟢"
            text = f"✅ **Room Updated Successfully**\n\n"
            text += f"{status_emoji} **Room ID:** {room_id}\n"
            text += f"**Name:** {updated_room.get('localityTitle', 'N/A')}\n"
            text += f"**Code:** {updated_room.get('localityCode', 'N/A')}\n"
            text += f"**Description:** {updated_room.get('localityDescription', 'N/A')}\n"
            text += f"**Ward:** {updated_room.get('parentLocalityTitle', 'N/A')}\n"
            text += f"**Status:** {'Occupied' if updated_room.get('isOccupied', False) else 'Available'}\n"
            if updated_room.get('facilities'):
                text += f"**Facilities:** {updated_room.get('facilities')}\n"
            text += f"**Last Updated:** {updated_room.get('updatedAt', 'Now')}\n"

            return [TextContent(type="text", text=text)]

        except Exception as e:
            return [TextContent(type="text", text=f"❌ Error updating room: {str(e)}")]


class CreateRoomTools(BaseTool):
    """Tools for creating new rooms"""

    def get_tool_definition(self) -> Tool:
        """Get create room tool definition"""
        return Tool(
            name="create_room",
            description="Create a new room",
            inputSchema={
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string",
                        "description": "Room name (localityTitle)"
                    },
                    "code": {
                        "type": "string",
                        "description": "Room code (localityCode)"
                    },
                    "ward_id": {
                        "type": "integer",
                        "description": "Parent ward ID (parentLocalityID)"
                    },
                    "description": {
                        "type": "string",
                        "description": "Room description (localityDescription)"
                    },
                    "facilities": {
                        "type": "string",
                        "description": "Facilities information"
                    }
                },
                "required": ["name", "code", "ward_id"]
            }
        )

    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute create room"""
        name = arguments.get("name")
        code = arguments.get("code")
        ward_id = arguments.get("ward_id")

        if not name or not code or not ward_id:
            return [TextContent(type="text", text="❌ Room name, code, and ward ID are required")]

        # Prepare room data
        room_data = {
            "localityTitle": name,
            "localityCode": code,
            "parentLocalityID": ward_id,
            "localityTypeID": 3,  # Room type
            "isActive": True,
            "isOccupied": False
        }

        if arguments.get("description"):
            room_data["localityDescription"] = arguments["description"]
        if arguments.get("facilities"):
            room_data["facilities"] = arguments["facilities"]

        try:
            # Create the room
            new_room = await self.client.create_room(room_data)

            # Format success response
            text = f"✅ **Room Created Successfully**\n\n"
            text += f"🟢 **Room ID:** {new_room.get('localityID', 'N/A')}\n"
            text += f"**Name:** {new_room.get('localityTitle', 'N/A')}\n"
            text += f"**Code:** {new_room.get('localityCode', 'N/A')}\n"
            text += f"**Description:** {new_room.get('localityDescription', 'N/A')}\n"
            text += f"**Ward ID:** {new_room.get('parentLocalityID', 'N/A')}\n"
            text += f"**Status:** Available\n"
            if new_room.get('facilities'):
                text += f"**Facilities:** {new_room.get('facilities')}\n"
            text += f"**Created:** {new_room.get('insertedAt', 'Now')}\n"

            return [TextContent(type="text", text=text)]

        except Exception as e:
            return [TextContent(type="text", text=f"❌ Error creating room: {str(e)}")]


class UpdateRoomByNameTools(BaseTool):
    """Tools for updating room by name"""

    def get_tool_definition(self) -> Tool:
        """Get update room by name tool definition"""
        return Tool(
            name="update_room_by_name",
            description="Update room information by name using the new API endpoint",
            inputSchema={
                "type": "object",
                "properties": {
                    "room_name": {
                        "type": "string",
                        "description": "The current name of the room to update"
                    },
                    "new_name": {
                        "type": "string",
                        "description": "New room name (localityTitle)"
                    },
                    "code": {
                        "type": "string",
                        "description": "New room code (localityCode)"
                    },
                    "description": {
                        "type": "string",
                        "description": "New room description (localityDescription)"
                    },
                    "facilities": {
                        "type": "string",
                        "description": "New facilities information"
                    },
                    "is_occupied": {
                        "type": "boolean",
                        "description": "Room occupancy status"
                    }
                },
                "required": ["room_name"]
            }
        )

    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute update room by name"""
        room_name = arguments.get("room_name")

        if not room_name:
            return [TextContent(type="text", text="❌ Room name is required")]

        # Prepare update data using UpdateLocalityDto format
        update_data = {}
        if arguments.get("new_name"):
            update_data["localityTitle"] = arguments["new_name"]
        if arguments.get("code"):
            update_data["localityCode"] = arguments["code"]
        if arguments.get("description"):
            update_data["localityDescription"] = arguments["description"]
        if arguments.get("facilities"):
            update_data["facilities"] = arguments["facilities"]
        if "is_occupied" in arguments:
            update_data["isOccupied"] = arguments["is_occupied"]

        if not update_data:
            return [TextContent(type="text", text="❌ At least one field must be provided for update")]

        try:
            # Update the room by name using the new API endpoint
            updated_room = await self.client.update_room_by_name(room_name, update_data)

            # Format success response
            status_emoji = "🔴" if updated_room.get('isOccupied', False) else "🟢"
            text = f"✅ **Room Updated Successfully**\n\n"
            text += f"{status_emoji} **Previous Name:** {room_name}\n"
            text += f"**Current Name:** {updated_room.get('localityTitle', 'N/A')}\n"
            text += f"**Code:** {updated_room.get('localityCode', 'N/A')}\n"
            text += f"**Description:** {updated_room.get('localityDescription', 'N/A')}\n"
            text += f"**Ward:** {updated_room.get('parentLocalityTitle', 'N/A')}\n"
            text += f"**Status:** {'Occupied' if updated_room.get('isOccupied', False) else 'Available'}\n"
            if updated_room.get('facilities'):
                text += f"**Facilities:** {updated_room.get('facilities')}\n"
            text += f"**Room ID:** {updated_room.get('localityID', 'N/A')}\n"
            text += f"**Last Updated:** {updated_room.get('updatedAt', 'Now')}\n"

            return [TextContent(type="text", text=text)]

        except Exception as e:
            return [TextContent(type="text", text=f"❌ Error updating room '{room_name}': {str(e)}")]
