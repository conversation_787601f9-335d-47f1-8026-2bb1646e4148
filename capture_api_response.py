#!/usr/bin/env python3
"""
Capture exact API response
"""

import asyncio
import httpx
import json

async def capture_api_response():
    """Capture exact API response"""
    print("📡 Capturing Exact API Response")
    print("=" * 60)
    
    base_url = "http://localhost:5077"
    
    async with httpx.AsyncClient() as client:
        
        # 1. Test the failed request (roomone)
        print("1️⃣ Testing failed request (roomone):")
        print("-" * 40)
        
        update_data = {
            "localityTitle": "Room1",
            "isActive": True,
            "localityCode": "R1"
        }
        
        try:
            response = await client.put(
                f"{base_url}/api/Configuration/rooms/update-by-name/roomone",
                json=update_data,
                timeout=10.0
            )
            
            print(f"Status Code: {response.status_code}")
            print(f"Status Text: {response.reason_phrase}")
            print(f"Headers:")
            for key, value in response.headers.items():
                print(f"  {key}: {value}")
            
            print(f"\nRaw Response Text:")
            print(response.text)
            
            print(f"\nParsed JSON Response:")
            try:
                json_response = response.json()
                print(json.dumps(json_response, indent=2))
            except:
                print("Could not parse as JSON")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # 2. Test with an existing room (Room1)
        print(f"\n2️⃣ Testing with existing room (Room1):")
        print("-" * 40)
        
        update_data = {
            "localityTitle": "UpdatedRoom1",
            "isActive": True,
            "localityCode": "UR1"
        }
        
        try:
            response = await client.put(
                f"{base_url}/api/Configuration/rooms/update-by-name/Room1",
                json=update_data,
                timeout=10.0
            )
            
            print(f"Status Code: {response.status_code}")
            print(f"Status Text: {response.reason_phrase}")
            print(f"Headers:")
            for key, value in response.headers.items():
                print(f"  {key}: {value}")
            
            print(f"\nRaw Response Text:")
            print(response.text)
            
            print(f"\nParsed JSON Response:")
            try:
                json_response = response.json()
                print(json.dumps(json_response, indent=2))
            except:
                print("Could not parse as JSON")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # 3. Test getting all rooms to see current state
        print(f"\n3️⃣ Getting all rooms (current state):")
        print("-" * 40)
        
        try:
            response = await client.get(f"{base_url}/api/Configuration/rooms")
            
            print(f"Status Code: {response.status_code}")
            print(f"Status Text: {response.reason_phrase}")
            
            print(f"\nRaw Response Text (first 500 chars):")
            print(response.text[:500] + "..." if len(response.text) > 500 else response.text)
            
            print(f"\nParsed JSON Response:")
            try:
                json_response = response.json()
                print("Rooms found:")
                for room in json_response.get('data', []):
                    print(f"  - '{room.get('localityTitle')}' (ID: {room.get('localityID')}, Code: {room.get('localityCode')})")
            except:
                print("Could not parse as JSON")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # 4. Test with different HTTP methods to see what's supported
        print(f"\n4️⃣ Testing different HTTP methods on update endpoint:")
        print("-" * 40)
        
        methods_to_test = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE']
        
        for method in methods_to_test:
            try:
                if method == 'GET':
                    response = await client.get(f"{base_url}/api/Configuration/rooms/update-by-name/Room1")
                elif method == 'POST':
                    response = await client.post(f"{base_url}/api/Configuration/rooms/update-by-name/Room1", json=update_data)
                elif method == 'PUT':
                    response = await client.put(f"{base_url}/api/Configuration/rooms/update-by-name/Room1", json=update_data)
                elif method == 'PATCH':
                    response = await client.patch(f"{base_url}/api/Configuration/rooms/update-by-name/Room1", json=update_data)
                elif method == 'DELETE':
                    response = await client.delete(f"{base_url}/api/Configuration/rooms/update-by-name/Room1")
                
                print(f"{method}: {response.status_code} - {response.reason_phrase}")
                if response.status_code not in [404, 405]:
                    print(f"  Response: {response.text[:100]}")
                    
            except Exception as e:
                print(f"{method}: Error - {str(e)[:50]}")

if __name__ == '__main__':
    asyncio.run(capture_api_response())
