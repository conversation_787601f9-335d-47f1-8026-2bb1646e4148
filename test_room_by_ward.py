#!/usr/bin/env python3
"""
Test room by ward functionality specifically
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_room_by_ward():
    """Test room by ward functionality"""
    print("🧪 Testing Room by Ward Functionality")
    print("=" * 60)
    
    try:
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # First, get actual ward names
        wards = await client.get_wards()
        print("Available wards:")
        for ward in wards[:5]:
            print(f"  - '{ward.get('localityTitle')}'")
        
        # Test direct API call first
        print(f"\n🔧 Direct API Test:")
        try:
            rooms = await client.get_rooms_by_ward_name("Surgical Ward")
            print(f"✅ Direct API: Found {len(rooms)} rooms in 'Surgical Ward'")
            for room in rooms[:3]:
                print(f"  - {room.get('localityTitle')}")
        except Exception as e:
            print(f"❌ Direct API error: {e}")
        
        # Test various room by ward queries
        test_queries = [
            "rooms in Surgical Ward",
            "show rooms in Surgical Ward",
            "list rooms of Surgical Ward",
            "rooms of Surgical Ward",
            "show me rooms in Surgical Ward"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{i}️⃣ Testing: '{query}'")
            print("-" * 50)
            
            try:
                result = await asyncio.wait_for(
                    chatbot.process_message('test-session', query),
                    timeout=25.0
                )
                
                print(f"Result type: {result.get('type')}")
                content = result.get('content', '')
                
                if result.get('type') == 'success':
                    print("✅ Query successful!")
                    # Check if it contains table
                    if '<table' in content:
                        print("✅ Contains table!")
                    else:
                        print("❌ No table found")
                    
                    # Check if it's about rooms or wards
                    if 'rooms in ward' in content.lower() or 'rooms in surgical ward' in content.lower():
                        print("✅ Shows rooms by ward!")
                    elif 'wards in the ipu system' in content.lower():
                        print("❌ Shows all wards instead of rooms")
                    else:
                        print("? Unknown response type")
                else:
                    print("❌ Query failed")
                
                # Show first few lines of response
                lines = content.split('\n')[:3]
                for line in lines:
                    if line.strip():
                        print(f"Response: {line.strip()}")
                
            except asyncio.TimeoutError:
                print("⏱️ Query timeout")
            except Exception as e:
                print(f"❌ Query error: {e}")
        
        print(f"\n✅ Room by ward test completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_room_by_ward())
