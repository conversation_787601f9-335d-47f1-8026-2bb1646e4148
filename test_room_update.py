#!/usr/bin/env python3
"""
Test room update functionality
"""

import asyncio
from llm_service import create_llm_chatbot
from ipu_client import create_client

async def test_room_update():
    """Test room update functionality"""
    print("🧪 Testing Room Update Functionality")
    print("=" * 50)
    
    try:
        # Create client and chatbot
        client = create_client()
        chatbot = create_llm_chatbot('mock', client)
        
        # Test 1: Update existing room
        print("1️⃣ Testing room update: 'Room 1' to 'NewRoom1'")
        result1 = await chatbot.process_message('test-session', 'update room name Room 1 to NewRoom1')
        print(f"   Type: {result1.get('type')}")
        print(f"   Content: {result1.get('content', 'No content')[:200]}...")
        
        # Test 2: Update building
        print("\n2️⃣ Testing building update: 'test333' to 'Buildingone'")
        result2 = await chatbot.process_message('test-session', 'update building name test333 to Buildingone')
        print(f"   Type: {result2.get('type')}")
        print(f"   Content: {result2.get('content', 'No content')[:200]}...")
        
        # Test 3: Update non-existent room
        print("\n3️⃣ Testing non-existent room update:")
        result3 = await chatbot.process_message('test-session', 'update room name NonExistentRoom to NewName')
        print(f"   Type: {result3.get('type')}")
        print(f"   Content: {result3.get('content', 'No content')[:200]}...")
        
        print("\n✅ All tests completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_room_update())
