#!/usr/bin/env python3
"""
Cleanup and Setup Script for Indici Assistant LLM Integration

This script:
1. Removes unnecessary files
2. Sets up the new LLM-integrated system
3. Provides setup instructions for Hugging Face
"""

import os
import shutil
from pathlib import Path

def cleanup_files():
    """Remove unnecessary files"""
    print("🧹 Cleaning up unnecessary files...")
    
    files_to_remove = [
        # Old web applications
        "demo_web.py",
        "llm_web_app.py",
        "start_llm_chat.py",
        
        # Old templates
        "templates/llm_chat.html",
        
        # Test files (keep main ones)
        "test_llm_chatbot.py",
        "test_llm_simple.py",
        "test_mcp_with_fallback.py",
        "simple_demo_test.py",
        "test_ipu_api_integration.py",
        
        # Documentation files (keep main ones)
        "PROFESSIONAL_LLM_CHATBOT_COMPLETE.md",
        "FINAL_COMPLETE_SYSTEM.md",
        "FINAL_INTEGRATION_REPORT.md",
        "IPU_API_INTEGRATION_SUMMARY.md",
        "BUILD_STATUS.md",
        
        # Demo files
        "demo.py",
        "indici_demo.html",
        
        # Old chatbot
        "chatbot.py",
    ]
    
    removed_count = 0
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"   ✅ Removed: {file_path}")
                removed_count += 1
            except Exception as e:
                print(f"   ❌ Failed to remove {file_path}: {e}")
        else:
            print(f"   ⚠️ Not found: {file_path}")
    
    print(f"\n📊 Cleanup complete: {removed_count} files removed")

def cleanup_cache():
    """Remove Python cache files"""
    print("\n🗑️ Cleaning up Python cache...")
    
    cache_dirs = [
        "__pycache__",
        "tools/__pycache__"
    ]
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                shutil.rmtree(cache_dir)
                print(f"   ✅ Removed cache: {cache_dir}")
            except Exception as e:
                print(f"   ❌ Failed to remove {cache_dir}: {e}")

def create_launcher():
    """Create a simple launcher script"""
    print("\n🚀 Creating launcher script...")
    
    launcher_content = '''#!/usr/bin/env python3
"""
Indici Assistant Launcher

Simple launcher for the integrated LLM system.
"""

import os
import sys

def main():
    print("🏥 Indici Assistant - Professional LLM Integration")
    print("=" * 60)
    
    # Check for LLM provider preferences
    print("🤖 LLM Provider Options:")
    print("1. Hugging Face (Local) - Set USE_HUGGINGFACE=true")
    print("2. OpenAI GPT-4 - Set OPENAI_API_KEY")
    print("3. Anthropic Claude - Set ANTHROPIC_API_KEY")
    print("4. Mock LLM (Demo) - No setup required")
    print()
    
    # Show current configuration
    print("📋 Current Configuration:")
    print(f"   • USE_HUGGINGFACE: {os.getenv('USE_HUGGINGFACE', 'false')}")
    print(f"   • HUGGINGFACE_MODEL: {os.getenv('HUGGINGFACE_MODEL', 'microsoft/DialoGPT-medium')}")
    print(f"   • OpenAI API Key: {'✅ Set' if os.getenv('OPENAI_API_KEY') else '❌ Not set'}")
    print(f"   • Anthropic API Key: {'✅ Set' if os.getenv('ANTHROPIC_API_KEY') else '❌ Not set'}")
    print()
    
    try:
        from indici_llm_app import run_app
        run_app()
    except ImportError as e:
        print(f"❌ Failed to import application: {e}")
        print("💡 Make sure all dependencies are installed: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
'''
    
    with open('start_indici.py', 'w') as f:
        f.write(launcher_content)
    
    print("   ✅ Created: start_indici.py")

def create_huggingface_setup():
    """Create Hugging Face setup instructions"""
    print("\n📚 Creating Hugging Face setup guide...")
    
    setup_content = '''# 🤗 Hugging Face Setup Guide

## Quick Setup for Local LLM

### 1. Install Dependencies
```bash
pip install transformers torch tokenizers accelerate
```

### 2. Set Environment Variables
```bash
# Use Hugging Face instead of API-based models
export USE_HUGGINGFACE=true

# Optional: Specify a different model
export HUGGINGFACE_MODEL=microsoft/DialoGPT-medium
```

### 3. Available Models

#### Recommended Models:
- **microsoft/DialoGPT-medium** (Default) - Good balance of quality and speed
- **microsoft/DialoGPT-large** - Better quality, slower
- **facebook/blenderbot-400M-distill** - Fast, good for basic conversations
- **microsoft/DialoGPT-small** - Fastest, basic quality

#### Advanced Models (Require more resources):
- **microsoft/DialoGPT-large** - High quality conversations
- **facebook/blenderbot-1B-distill** - Better understanding
- **EleutherAI/gpt-neo-1.3B** - More general knowledge

### 4. GPU Support (Optional)
If you have a CUDA-compatible GPU:
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 5. Start the Application
```bash
python start_indici.py
```

## Model Comparison

| Model | Size | Speed | Quality | Memory |
|-------|------|-------|---------|---------|
| DialoGPT-small | 117M | Fast | Basic | 1GB |
| DialoGPT-medium | 345M | Medium | Good | 2GB |
| DialoGPT-large | 762M | Slow | High | 4GB |
| BlenderBot-400M | 400M | Fast | Good | 2GB |

## Troubleshooting

### Common Issues:
1. **Out of Memory** - Use a smaller model
2. **Slow Performance** - Enable GPU or use smaller model
3. **Model Download Fails** - Check internet connection

### Performance Tips:
- Use GPU if available
- Start with smaller models
- Close other applications to free memory
'''
    
    with open('HUGGINGFACE_SETUP.md', 'w') as f:
        f.write(setup_content)
    
    print("   ✅ Created: HUGGINGFACE_SETUP.md")

def update_readme():
    """Update the main README"""
    print("\n📝 Updating README...")
    
    readme_update = '''
## 🚀 Quick Start

### Option 1: Hugging Face (Local LLM)
```bash
# Install dependencies
pip install -r requirements.txt

# Enable Hugging Face
export USE_HUGGINGFACE=true

# Start application
python start_indici.py
```

### Option 2: OpenAI GPT-4
```bash
# Set API key
export OPENAI_API_KEY="your_openai_key"

# Start application
python start_indici.py
```

### Option 3: Anthropic Claude
```bash
# Set API key
export ANTHROPIC_API_KEY="your_anthropic_key"

# Start application
python start_indici.py
```

### Option 4: Demo Mode
```bash
# No setup required - uses mock responses
python start_indici.py
```

Open your browser to: http://localhost:5000
'''
    
    # Read existing README
    try:
        with open('README.md', 'r') as f:
            content = f.read()
        
        # Find where to insert the quick start
        if '## 🚀 Quick Start' in content:
            print("   ⚠️ Quick start section already exists in README")
        else:
            # Insert after the header
            lines = content.split('\n')
            insert_pos = 3  # After the main description
            lines.insert(insert_pos, readme_update)
            
            with open('README.md', 'w') as f:
                f.write('\n'.join(lines))
            
            print("   ✅ Updated README.md with quick start guide")
    
    except Exception as e:
        print(f"   ❌ Failed to update README: {e}")

def main():
    """Main cleanup and setup function"""
    print("🏥 Indici Assistant - Cleanup and Setup")
    print("=" * 50)
    
    # Cleanup
    cleanup_files()
    cleanup_cache()
    
    # Setup
    create_launcher()
    create_huggingface_setup()
    update_readme()
    
    print("\n" + "=" * 50)
    print("✅ Cleanup and setup complete!")
    print("\n🚀 Next Steps:")
    print("1. Choose your LLM provider:")
    print("   • Hugging Face: export USE_HUGGINGFACE=true")
    print("   • OpenAI: export OPENAI_API_KEY=your_key")
    print("   • Anthropic: export ANTHROPIC_API_KEY=your_key")
    print("   • Demo: No setup required")
    print("\n2. Install dependencies:")
    print("   pip install -r requirements.txt")
    print("\n3. Start the application:")
    print("   python start_indici.py")
    print("\n4. Open browser:")
    print("   http://localhost:5000")
    print("\n📚 For Hugging Face setup: see HUGGINGFACE_SETUP.md")

if __name__ == '__main__':
    main()
