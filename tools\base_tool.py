"""
Base tool class for MCP tools
"""

import json
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from mcp.types import Tool, TextContent
from ipu_client import IPUClient

logger = logging.getLogger(__name__)


class BaseTool(ABC):
    """Base class for all MCP tools"""
    
    def __init__(self, client: IPUClient):
        self.client = client
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def get_tool_definition(self) -> Tool:
        """Return the MCP tool definition"""
        pass
    
    @abstractmethod
    async def execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute the tool with given arguments"""
        pass
    
    def format_json_response(self, data: Any, title: str = "Result") -> str:
        """Format data as JSON with title"""
        json_str = json.dumps(data, indent=2, ensure_ascii=False)
        return f"{title}:\n\n```json\n{json_str}\n```"
    
    def format_table_response(self, data: List[Dict], columns: List[str], title: str = "Results") -> str:
        """Format data as a simple table"""
        if not data:
            return f"{title}: No data found"
        
        # Create header
        header = " | ".join(columns)
        separator = " | ".join(["-" * len(col) for col in columns])
        
        # Create rows
        rows = []
        for item in data:
            row_values = []
            for col in columns:
                value = item.get(col, "")
                if isinstance(value, bool):
                    value = "Yes" if value else "No"
                elif value is None:
                    value = ""
                else:
                    value = str(value)
                row_values.append(value[:20])  # Truncate long values
            rows.append(" | ".join(row_values))
        
        table = f"{title} ({len(data)} items):\n\n{header}\n{separator}\n" + "\n".join(rows)
        return table
    
    def format_summary_response(self, data: List[Dict], summary_fields: List[str], title: str = "Summary") -> str:
        """Format data as a summary with key information"""
        if not data:
            return f"{title}: No data found"
        
        summaries = []
        for i, item in enumerate(data, 1):
            summary_parts = [f"{i}."]
            for field in summary_fields:
                value = item.get(field)
                if value is not None:
                    if isinstance(value, bool):
                        value = "Yes" if value else "No"
                    summary_parts.append(f"{field.title()}: {value}")
            summaries.append(" ".join(summary_parts))
        
        return f"{title} ({len(data)} items):\n\n" + "\n".join(summaries)
    
    def create_error_response(self, error_message: str) -> List[TextContent]:
        """Create an error response"""
        return [TextContent(type="text", text=f"❌ Error: {error_message}")]
    
    def create_success_response(self, message: str) -> List[TextContent]:
        """Create a success response"""
        return [TextContent(type="text", text=f"✅ {message}")]
    
    def create_info_response(self, message: str) -> List[TextContent]:
        """Create an info response"""
        return [TextContent(type="text", text=f"ℹ️ {message}")]
    
    def validate_required_args(self, arguments: Dict[str, Any], required_fields: List[str]) -> Optional[str]:
        """Validate that required arguments are present"""
        missing_fields = []
        for field in required_fields:
            if field not in arguments or arguments[field] is None:
                missing_fields.append(field)
        
        if missing_fields:
            return f"Missing required arguments: {', '.join(missing_fields)}"
        return None
    
    def validate_integer_arg(self, arguments: Dict[str, Any], field: str) -> Optional[str]:
        """Validate that an argument is a valid integer"""
        value = arguments.get(field)
        if value is None:
            return f"Missing argument: {field}"
        
        try:
            int(value)
            return None
        except (ValueError, TypeError):
            return f"Invalid integer value for {field}: {value}"
    
    async def safe_execute(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Safely execute the tool with error handling"""
        try:
            self.logger.info(f"Executing {self.__class__.__name__} with args: {arguments}")
            return await self.execute(arguments)
        except Exception as e:
            self.logger.error(f"Error executing {self.__class__.__name__}: {e}")
            return self.create_error_response(str(e))


class ToolRegistry:
    """Registry for managing MCP tools"""
    
    def __init__(self):
        self.tools: Dict[str, BaseTool] = {}
    
    def register(self, tool: BaseTool):
        """Register a tool"""
        tool_def = tool.get_tool_definition()
        self.tools[tool_def.name] = tool
        logger.info(f"Registered tool: {tool_def.name}")
    
    def get_tool(self, name: str) -> Optional[BaseTool]:
        """Get a tool by name"""
        return self.tools.get(name)
    
    def get_all_tools(self) -> List[Tool]:
        """Get all tool definitions"""
        return [tool.get_tool_definition() for tool in self.tools.values()]
    
    def get_tool_names(self) -> List[str]:
        """Get all tool names"""
        return list(self.tools.keys())
    
    async def execute_tool(self, name: str, arguments: Dict[str, Any]) -> List[TextContent]:
        """Execute a tool by name"""
        tool = self.get_tool(name)
        if not tool:
            return [TextContent(type="text", text=f"❌ Unknown tool: {name}")]
        
        return await tool.safe_execute(arguments)
